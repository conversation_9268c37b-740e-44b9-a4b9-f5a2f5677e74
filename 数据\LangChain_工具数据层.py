"""
LangChain工具数据层
负责处理工具配置、智能体工具关联等数据库操作
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from 数据.Postgre_异步连接池 import Postgre_异步数据库连接池

# PostgreSQL连接池导入
from 数据.Postgre_异步连接池 import Postgre_异步连接池实例 as 异步连接池实例

# 配置日志
工具数据层日志器 = logging.getLogger("LangChain工具数据层")


class LangChain工具数据层:
    """LangChain工具数据层 - 优雅架构设计

    架构原则：
    1. 依赖注入 - 构造时注入所有依赖
    2. 类型安全 - 完全的类型保证
    3. 单一职责 - 专注于工具数据操作
    4. 优雅初始化 - 避免复杂的异步初始化
    """

    def __init__(self, 数据库操作: Optional[Postgre_异步数据库连接池] = None):
        """构造函数 - 依赖注入模式

        Args:
            数据库操作: PostgreSQL连接池实例，如果为None则使用默认实例
        """
        # 依赖注入 - 确保数据库操作永远不为None
        self.数据库操作: Postgre_异步数据库连接池 = 数据库操作 or 异步连接池实例
        self.已初始化 = True  # 简化初始化逻辑

        工具数据层日志器.info("LangChain工具数据层创建成功")

    async def 初始化(self):
        """初始化工具数据层"""
        try:
            # 确保连接池已初始化
            if not self.数据库操作.已初始化:
                await self.数据库操作.初始化数据库连接池()

            工具数据层日志器.info("LangChain工具数据层初始化成功")
        except Exception as e:
            工具数据层日志器.error(f"LangChain工具数据层初始化失败: {str(e)}")
            raise

    @classmethod
    async def 创建实例(cls) -> "LangChain工具数据层":
        """异步工厂方法 - 确保所有依赖都已初始化"""
        # 确保连接池已初始化
        if not 异步连接池实例.已初始化:
            await 异步连接池实例.初始化数据库连接池()

        # 创建数据层实例
        实例 = cls(异步连接池实例)

        # 执行异步初始化逻辑
        await 实例.初始化()

        return 实例

    # ==================== 工具配置管理 ====================

    async def 获取工具配置列表(
        self, 工具类型: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取工具配置列表"""
        try:
            条件列表 = []
            参数列表 = []

            参数索引 = 1
            if 工具类型:
                条件列表.append("工具类型 = $1")
                参数列表.append(工具类型)
                参数索引 += 1

            WHERE子句 = f"WHERE {' AND '.join(条件列表)}" if 条件列表 else ""

            查询SQL = f"""
            SELECT
                id, 工具名称, 工具描述, 工具参数, 超时时间,
                创建时间, 更新时间
            FROM langchain_工具配置表
            {WHERE子句}
            ORDER BY 工具名称
            """

            结果 = await self.数据库操作.执行查询(
                查询SQL, tuple(参数列表) if 参数列表 else None
            )

            # 处理数据字段
            工具列表 = []
            for 行 in 结果:
                工具配置 = dict(行)

                # 直接使用字段值，不进行JSON解析
                工具配置["工具参数"] = 工具配置.get("工具参数") or ""
                工具配置["超时时间"] = 工具配置.get("超时时间") or 30

                工具列表.append(工具配置)

            工具数据层日志器.debug(f"获取工具配置列表成功，共 {len(工具列表)} 个工具")
            return 工具列表

        except Exception as e:
            工具数据层日志器.error(f"获取工具配置列表失败: {str(e)}")
            return []

    async def 获取工具配置(self, 工具名称: str) -> Optional[Dict[str, Any]]:
        """获取单个工具配置（通过工具名称）"""
        try:
            查询SQL = """
            SELECT
                id, 工具名称, 工具描述, 工具参数, 超时时间,
                创建时间, 更新时间
            FROM langchain_工具配置表
            WHERE 工具名称 = $1
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (工具名称,))

            if not 结果:
                return None

            工具配置 = dict(结果[0])

            # 直接使用字段值，不进行JSON解析
            工具配置["工具参数"] = 工具配置.get("工具参数") or ""
            工具配置["超时时间"] = 工具配置.get("超时时间") or 30

            return 工具配置

        except Exception as e:
            工具数据层日志器.error(f"获取工具配置失败 ({工具名称}): {str(e)}")
            return None

    async def 根据ID获取工具配置(self, 工具id: int) -> Optional[Dict[str, Any]]:
        """获取单个工具配置（通过工具id）"""
        try:
            查询SQL = """
            SELECT
                id, 工具名称, 工具描述, 工具参数, 超时时间,
                创建时间, 更新时间
            FROM langchain_工具配置表
            WHERE id = $1
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (工具id,))

            if not 结果:
                return None

            工具配置 = dict(结果[0])

            # 直接使用字段值，不进行JSON解析
            工具配置["工具参数"] = 工具配置.get("工具参数") or ""
            工具配置["超时时间"] = 工具配置.get("超时时间") or 30

            return 工具配置

        except Exception as e:
            工具数据层日志器.error(f"根据ID获取工具配置失败 (ID: {工具id}): {str(e)}")
            return None

    async def 根据名称获取工具配置(self, 工具名称: str) -> Optional[Dict[str, Any]]:
        """获取单个工具配置（通过工具名称）"""
        try:
            查询SQL = """
            SELECT
                id, 工具名称, 工具描述, 工具参数, 超时时间,
                创建时间, 更新时间
            FROM langchain_工具配置表
            WHERE 工具名称 = $1
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (工具名称,))

            if not 结果:
                return None

            工具配置 = dict(结果[0])

            return 工具配置

        except Exception as e:
            工具数据层日志器.error(
                f"根据名称获取工具配置失败 (名称: {工具名称}): {str(e)}"
            )
            return None

    async def 插入工具配置(self, 工具配置: Dict[str, Any]) -> bool:
        """插入工具配置到数据库 - 纯数据层操作"""
        try:
            插入SQL = """
            INSERT INTO langchain_工具配置表
            (工具名称, 工具描述, 工具参数, 超时时间, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            """
            await self.数据库操作.执行插入(
                插入SQL,
                (
                    工具配置["工具名称"],
                    工具配置["工具描述"],
                    工具配置["工具参数"],
                    工具配置.get("超时时间", 30),
                ),
            )
            return True
        except Exception as e:
            工具数据层日志器.error(f"插入工具配置失败: {str(e)}")
            return False

    async def 更新工具配置(self, 工具名称: str, 工具配置: Dict[str, Any]) -> bool:
        """更新工具配置 - 纯数据层操作"""
        try:
            更新SQL = """
            UPDATE langchain_工具配置表
            SET 工具描述 = $2, 工具参数 = $3, 超时时间 = $4, 更新时间 = CURRENT_TIMESTAMP
            WHERE 工具名称 = $1
            """
            await self.数据库操作.执行更新(
                更新SQL,
                (
                    工具名称,
                    工具配置["工具描述"],
                    工具配置["工具参数"],
                    工具配置.get("超时时间", 30),
                ),
            )
            return True
        except Exception as e:
            工具数据层日志器.error(f"更新工具配置失败 ({工具名称}): {str(e)}")
            return False

    async def 获取现有工具映射(self) -> Dict[str, int]:
        """获取现有工具名称到ID的映射 - 纯数据层操作"""
        try:
            查询SQL = "SELECT 工具名称, id FROM langchain_工具配置表"
            结果 = await self.数据库操作.执行查询(查询SQL)
            return {row["工具名称"]: row["id"] for row in 结果} if 结果 else {}
        except Exception as e:
            工具数据层日志器.error(f"获取现有工具映射失败: {str(e)}")
            return {}

    async def 创建工具配置(self, 工具配置: Dict[str, Any]) -> Optional[int]:
        """创建工具配置"""
        try:
            工具名称 = 工具配置.get("工具名称")
            if not 工具名称:
                工具数据层日志器.error("工具名称不能为空")
                return None

            # 先检查工具是否已存在
            现有工具 = await self.获取工具配置(工具名称)
            if 现有工具:
                工具数据层日志器.info(
                    f"工具配置已存在: {工具名称} (ID: {现有工具.get('id')})"
                )
                return 现有工具.get("id")

            插入SQL = """
            INSERT INTO langchain_工具配置表
            (工具名称, 工具描述, 工具参数, 超时时间, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
            """

            # 直接使用字符串格式存储
            工具参数 = 工具配置.get("工具参数", "")
            if isinstance(工具参数, dict):
                工具参数 = str(工具参数)

            参数 = [
                工具名称,
                工具配置.get("工具描述", ""),
                工具参数,
                工具配置.get("超时时间", 30),
            ]

            结果 = await self.数据库操作.执行查询(插入SQL, tuple(参数))
            工具id = 结果[0]["id"] if 结果 else None

            if 工具id:
                工具数据层日志器.info(f"✅ 创建工具配置成功: {工具名称} (ID: {工具id})")
                return 工具id
            else:
                工具数据层日志器.error(f"❌ 创建工具配置失败: {工具名称}")
                return None

        except Exception as e:
            # 如果是重复键错误，尝试获取现有工具的ID
            if "Duplicate entry" in str(e) and "工具名称" in str(e):
                工具名称 = 工具配置.get("工具名称")
                if 工具名称:
                    工具数据层日志器.info(f"工具配置已存在，获取现有ID: {工具名称}")
                    现有工具 = await self.获取工具配置(工具名称)
                    if 现有工具:
                        return 现有工具.get("id")

            工具数据层日志器.error(
                f"创建工具配置异常 ({工具配置.get('工具名称')}): {str(e)}"
            )
            return None

    async def 删除工具配置(self, 工具名称: str) -> bool:
        """删除工具配置（先删除关联记录，再删除工具配置）"""
        try:
            # 先获取工具ID
            工具配置 = await self.获取工具配置(工具名称)
            if not 工具配置:
                工具数据层日志器.warning(f"工具配置不存在: {工具名称}")
                return True  # 工具不存在，认为删除成功

            工具id = 工具配置.get("id")

            # 先删除智能体工具关联记录
            删除关联SQL = "DELETE FROM langchain_智能体工具关联表 WHERE langchain_工具配置表id = $1"
            await self.数据库操作.执行更新(删除关联SQL, (工具id,))
            工具数据层日志器.debug(f"删除工具关联记录: {工具名称} (ID: {工具id})")

            # 再删除工具配置
            删除工具SQL = "DELETE FROM langchain_工具配置表 WHERE 工具名称 = $1"
            await self.数据库操作.执行更新(删除工具SQL, (工具名称,))
            工具数据层日志器.info(f"✅ 删除工具配置成功: {工具名称}")
            return True

        except Exception as e:
            工具数据层日志器.error(f"删除工具配置失败 ({工具名称}): {str(e)}")
            return False

    # ==================== 智能体工具关联管理 ====================

    async def 获取智能体工具关联(self, 智能体id: int) -> List[Dict[str, Any]]:
        """获取智能体的工具关联 - 使用工具配置表ID"""
        try:
            查询SQL = """
            SELECT
                a.langchain_智能体配置表id,
                a.langchain_工具配置表id,
                a.工具名称,
                a.工具配置,
                a.启用状态,
                a.创建时间,
                t.工具描述,
                t.工具参数,
                t.超时时间
            FROM langchain_智能体工具关联表 a
            LEFT JOIN langchain_工具配置表 t ON a.langchain_工具配置表id = t.id
            WHERE a.langchain_智能体配置表id = $1 AND a.启用状态 = 1
            ORDER BY a.创建时间
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (智能体id,))

            关联列表 = []
            for 行 in 结果:
                关联数据 = dict(行)

                # 直接使用字段值，不进行JSON解析
                关联数据["工具配置"] = 关联数据.get("工具配置") or ""
                关联数据["工具参数"] = 关联数据.get("工具参数") or ""
                关联数据["超时时间"] = 关联数据.get("超时时间") or 30

                # 转换布尔值
                关联数据["启用状态"] = bool(关联数据.get("启用状态", 0))

                关联列表.append(关联数据)

            工具数据层日志器.debug(
                f"获取智能体工具关联成功，智能体 {智能体id} 有 {len(关联列表)} 个工具"
            )
            return 关联列表

        except Exception as e:
            工具数据层日志器.error(
                f"获取智能体工具关联失败 (智能体id: {智能体id}): {str(e)}"
            )
            return []

    async def 获取单个智能体工具关联(
        self, 智能体id: int, 工具id: int
    ) -> Optional[Dict[str, Any]]:
        """获取智能体的单个工具关联"""
        try:
            查询SQL = """
            SELECT
                a.langchain_智能体配置表id,
                a.langchain_工具配置表id,
                a.工具名称,
                a.工具配置,
                a.启用状态,
                a.创建时间,
                t.工具描述,
                t.工具参数,
                t.超时时间
            FROM langchain_智能体工具关联表 a
            LEFT JOIN langchain_工具配置表 t ON a.langchain_工具配置表id = t.id
            WHERE a.langchain_智能体配置表id = $1 AND a.langchain_工具配置表id = $2
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (智能体id, 工具id))

            if 结果:
                关联数据 = dict(结果[0])

                # 解析JSON字段
                json_字段 = ["工具配置", "工具参数"]
                for 字段 in json_字段:
                    if 关联数据.get(字段):
                        try:
                            关联数据[字段] = json.loads(关联数据[字段])
                        except (json.JSONDecodeError, TypeError):
                            关联数据[字段] = {}

                # 转换布尔值
                关联数据["启用状态"] = bool(关联数据.get("启用状态", 0))

                工具数据层日志器.debug(
                    f"获取单个工具关联成功: 智能体{智能体id} -> 工具{工具id}"
                )
                return 关联数据
            else:
                工具数据层日志器.debug(
                    f"工具关联不存在: 智能体{智能体id} -> 工具{工具id}"
                )
                return None

        except Exception as e:
            工具数据层日志器.error(
                f"获取单个工具关联失败 (智能体{智能体id} -> 工具{工具id}): {str(e)}"
            )
            return None

    async def 根据ID获取工具配置详情(self, 工具配置id: int) -> Dict[str, Any]:
        """根据工具配置表ID获取工具配置"""
        try:
            查询SQL = """
            SELECT
                id, 工具名称, 工具描述, 工具参数, 超时时间,
                创建时间, 更新时间
            FROM langchain_工具配置表
            WHERE id = $1
            """

            结果 = await self.数据库操作.执行查询(查询SQL, (工具配置id,))

            if 结果:
                工具配置 = dict(结果[0])

                # 直接使用字段值，不进行JSON解析
                工具配置["工具参数"] = 工具配置.get("工具参数") or ""
                工具配置["超时时间"] = 工具配置.get("超时时间") or 30

                工具数据层日志器.debug(
                    f"获取工具配置成功: ID={工具配置id}, 名称={工具配置.get('工具名称')}"
                )
                return 工具配置
            else:
                工具数据层日志器.warning(f"工具配置不存在: ID={工具配置id}")
                return {}

        except Exception as e:
            工具数据层日志器.error(f"获取工具配置失败 (ID: {工具配置id}): {str(e)}")
            return {}

    async def 创建或更新智能体工具关联(
        self,
        智能体id: int,
        工具id: int,
        工具配置: Optional[Dict[str, Any]] = None,
        启用状态: bool = True,
    ) -> bool:
        """创建或更新智能体工具关联（使用UPSERT）"""
        try:
            # 获取工具名称
            工具配置信息 = await self.根据ID获取工具配置详情(工具id)
            if not 工具配置信息:
                工具数据层日志器.error(f"工具ID {工具id} 不存在于工具配置表中")
                return False

            工具名称 = 工具配置信息.get("工具名称")
            工具配置_str = json.dumps(工具配置) if 工具配置 else "{}"

            # 先查询是否存在
            现有关联 = await self.获取单个智能体工具关联(智能体id, 工具id)

            if 现有关联:
                # 更新现有记录
                更新SQL = """
                UPDATE langchain_智能体工具关联表
                SET 工具配置 = $3, 启用状态 = $4, 创建时间 = CURRENT_TIMESTAMP
                WHERE langchain_智能体配置表id = $1 AND langchain_工具配置表id = $2
                """
                await self.数据库操作.执行更新(
                    更新SQL, (智能体id, 工具id, 工具配置_str, 1 if 启用状态 else 0)
                )
                操作类型 = "更新"
            else:
                # 插入新记录
                插入SQL = """
                INSERT INTO langchain_智能体工具关联表
                (langchain_智能体配置表id, langchain_工具配置表id, 工具名称, 工具配置, 启用状态, 创建时间)
                VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
                """
                await self.数据库操作.执行更新(
                    插入SQL,
                    (智能体id, 工具id, 工具名称, 工具配置_str, 1 if 启用状态 else 0),
                )
                操作类型 = "创建"

            工具数据层日志器.info(
                f"✅ 智能体工具关联{操作类型}成功: 智能体{智能体id} -> 工具{工具id}({工具名称}) (启用: {启用状态})"
            )
            return True

        except Exception as e:
            工具数据层日志器.error(
                f"智能体工具关联操作失败 (智能体{智能体id} -> 工具{工具id}): {str(e)}"
            )
            return False

    async def 更新智能体工具关联(
        self, 智能体id: int, 工具名称: str, 更新数据: Dict[str, Any]
    ) -> bool:
        """更新智能体工具关联"""
        try:
            设置子句 = []
            参数列表 = []

            参数索引 = 1
            if "工具配置" in 更新数据:
                设置子句.append(f"工具配置 = ${参数索引}")
                参数索引 += 1
                工具配置 = 更新数据["工具配置"]
                if isinstance(工具配置, dict):
                    工具配置 = str(工具配置)
                参数列表.append(工具配置)

            if "启用状态" in 更新数据:
                设置子句.append(f"启用状态 = ${参数索引}")
                参数索引 += 1
                参数列表.append(1 if 更新数据["启用状态"] else 0)

            if not 设置子句:
                return True

            参数列表.extend([智能体id, 工具名称])

            更新SQL = f"""
            UPDATE langchain_智能体工具关联表
            SET {", ".join(设置子句)}
            WHERE langchain_智能体配置表id = $1 AND 工具名称 = $2
            """

            影响行数 = await self.数据库操作.执行更新(更新SQL, tuple(参数列表))

            if 影响行数 > 0:
                工具数据层日志器.info(
                    f"✅ 更新智能体工具关联成功: 智能体{智能体id} -> {工具名称}"
                )
                return True
            else:
                工具数据层日志器.warning(
                    f"⚠️ 更新智能体工具关联无影响: 智能体{智能体id} -> {工具名称}"
                )
                return False

        except Exception as e:
            工具数据层日志器.error(
                f"更新智能体工具关联失败 (智能体{智能体id} -> {工具名称}): {str(e)}"
            )
            return False

    async def 删除智能体工具关联(
        self, 智能体id: int, 工具id: Optional[int] = None
    ) -> bool:
        """删除智能体工具关联"""
        try:
            if 工具id:
                # 删除特定工具关联
                删除SQL = "DELETE FROM langchain_智能体工具关联表 WHERE langchain_智能体配置表id = $1 AND langchain_工具配置表id = $2"
                参数 = [智能体id, 工具id]
                描述 = f"智能体{智能体id} -> 工具{工具id}"
            else:
                # 删除智能体的所有工具关联
                删除SQL = "DELETE FROM langchain_智能体工具关联表 WHERE langchain_智能体配置表id = $1"
                参数 = [智能体id]
                描述 = f"智能体{智能体id}的所有工具关联"

            await self.数据库操作.执行更新(删除SQL, tuple(参数))
            工具数据层日志器.info(f"✅ 删除智能体工具关联成功: {描述}")
            return True

        except Exception as e:
            工具数据层日志器.error(f"删除智能体工具关联失败: {str(e)}")
            return False

    async def 批量更新智能体工具关联(
        self, 智能体id: int, 工具列表: List[Dict[str, Any]]
    ) -> bool:
        """批量更新智能体工具关联 - 使用工具配置表ID"""
        try:
            # 先删除现有关联
            await self.删除智能体工具关联(智能体id)

            # 批量插入新关联
            if not 工具列表:
                工具数据层日志器.info(f"✅ 智能体{智能体id}工具关联已清空")
                return True

            插入SQL = """
            INSERT INTO langchain_智能体工具关联表
            (langchain_智能体配置表id, langchain_工具配置表id, 工具名称, 工具配置, 启用状态, 创建时间)
            VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
            """

            批量参数 = []
            for 工具配置 in 工具列表:
                工具名称 = 工具配置.get("name") or 工具配置.get("工具名称")
                if not 工具名称:
                    continue

                # 检查工具是否启用，默认为True
                启用状态 = 工具配置.get("enabled", True)
                # 如果enabled明确设置为False，则跳过该工具（不创建关联）
                if 启用状态 is False:
                    工具数据层日志器.debug(f"跳过禁用的工具: {工具名称}")
                    continue

                # 根据工具名称获取工具配置表ID
                工具配置id = await self._获取工具配置id(工具名称)
                if not 工具配置id:
                    工具数据层日志器.warning(f"工具配置不存在，跳过: {工具名称}")
                    continue

                参数 = [
                    智能体id,
                    工具配置id,
                    工具名称,
                    json.dumps(工具配置, ensure_ascii=False),
                    1,  # 只有启用的工具才会被添加到关联表
                ]
                批量参数.append(参数)

            if 批量参数:
                # 转换为元组列表
                批量元组参数 = [tuple(参数) for 参数 in 批量参数]
                # 逐个插入，因为PostgreSQL连接池没有批量插入方法
                成功数量 = 0
                for 参数 in 批量元组参数:
                    try:
                        await self.数据库操作.执行更新(插入SQL, 参数)
                        成功数量 += 1
                    except Exception as e:
                        工具数据层日志器.error(f"插入工具关联失败: {str(e)}")
                工具数据层日志器.info(
                    f"✅ 批量更新智能体工具关联成功: 智能体{智能体id} 关联{成功数量}个工具"
                )
                return 成功数量 == len(批量参数)
            else:
                工具数据层日志器.info(f"✅ 智能体{智能体id}无有效工具配置")
                return True

        except Exception as e:
            工具数据层日志器.error(
                f"批量更新智能体工具关联失败 (智能体{智能体id}): {str(e)}"
            )
            return False

    async def _获取工具配置id(self, 工具名称: str) -> Optional[int]:
        """根据工具名称获取工具配置表ID"""
        try:
            查询SQL = "SELECT id FROM langchain_工具配置表 WHERE 工具名称 = $1"
            结果 = await self.数据库操作.执行查询(查询SQL, (工具名称,))

            if 结果:
                return 结果[0]["id"]
            else:
                return None

        except Exception as e:
            工具数据层日志器.error(f"获取工具配置id失败 ({工具名称}): {str(e)}")
            return None

    # ==================== 工具调用统计 ====================

    async def 记录工具调用(
        self, 工具名称: str, 成功: bool = True, 执行时间: float = 0.0
    ) -> bool:
        """记录工具调用统计 - 由于统计字段已删除，此方法仅记录日志"""
        try:
            工具数据层日志器.debug(
                f"工具调用记录: {工具名称} ({'成功' if 成功 else '失败'}), 执行时间: {执行时间:.2f}s"
            )
            return True

        except Exception as e:
            工具数据层日志器.error(f"记录工具调用统计失败 ({工具名称}): {str(e)}")
            return False

    async def 批量更新工具统计(self, 统计数据: List[Dict[str, Any]]) -> bool:
        """批量更新工具统计信息"""
        try:
            工具数据层日志器.info(f"工具统计记录: 共 {len(统计数据)} 个工具调用")
            for 统计 in 统计数据:
                工具名称 = 统计.get("工具名称", "未知")
                使用次数 = 统计.get("使用次数增量", 0)
                成功次数 = 统计.get("成功次数增量", 0)
                失败次数 = 统计.get("失败次数增量", 0)
                工具数据层日志器.debug(
                    f"工具 {工具名称}: 使用{使用次数}次, 成功{成功次数}次, 失败{失败次数}次"
                )
            return True

        except Exception as e:
            工具数据层日志器.error(f"批量更新工具统计失败: {str(e)}")
            return False

    async def 记录工具调用日志(
        self,
        用户ID: int,
        智能体id: Optional[int],
        工具名称: str,
        调用参数: str,
        执行结果: str,
        执行状态: str,
        执行时间: float,
    ) -> bool:
        """记录工具调用日志到日志表"""
        try:
            插入SQL = """
            INSERT INTO langchain_工具调用日志表
            (用户id, 智能体id, 工具名称, 调用参数, 执行结果, 执行状态, 执行时间, 调用时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
            """

            await self.数据库操作.执行插入(
                插入SQL,
                (用户ID, 智能体id, 工具名称, 调用参数, 执行结果, 执行状态, 执行时间),
            )

            工具数据层日志器.debug(f"记录工具调用日志成功: {工具名称} (用户: {用户ID})")
            return True

        except Exception as e:
            工具数据层日志器.error(f"记录工具调用日志失败 ({工具名称}): {str(e)}")
            return False

    # ==================== MCP工具管理 ====================

    async def 获取MCP工具列表(
        self, 启用状态: Optional[bool] = None
    ) -> List[Dict[str, Any]]:
        """获取MCP工具列表"""
        try:
            if 启用状态 is not None:
                查询SQL = """
                SELECT id, 工具名称, 工具类型, 显示名称, 工具描述, 配置参数, 连接配置, 启用状态, 创建时间, 更新时间
                FROM langchain_mcp工具表
                WHERE 启用状态 = $1
                ORDER BY 创建时间 DESC
                """
                参数 = [1 if 启用状态 else 0]
            else:
                查询SQL = """
                SELECT id, 工具名称, 工具类型, 显示名称, 工具描述, 配置参数, 连接配置, 启用状态, 创建时间, 更新时间
                FROM langchain_mcp工具表
                ORDER BY 创建时间 DESC
                """
                参数 = []

            结果 = await self.数据库操作.执行查询(查询SQL, 参数)
            工具数据层日志器.debug(f"获取MCP工具列表成功，共 {len(结果)} 个工具")
            return 结果

        except Exception as e:
            工具数据层日志器.error(f"获取MCP工具列表失败: {str(e)}")
            return []

    async def 添加MCP工具(
        self,
        工具名称: str,
        工具类型: str,
        显示名称: str,
        工具描述: Optional[str] = None,
        配置参数: Optional[str] = None,
        连接配置: Optional[str] = None,
        启用状态: bool = True,
    ) -> Optional[int]:
        """添加MCP工具"""
        try:
            插入SQL = """
            INSERT INTO langchain_mcp工具表
            (工具名称, 工具类型, 显示名称, 工具描述, 配置参数, 连接配置, 启用状态, 创建时间, 更新时间)
            VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            RETURNING id
            """

            结果 = await self.数据库操作.执行查询(
                插入SQL,
                (
                    工具名称,
                    工具类型,
                    显示名称,
                    工具描述,
                    配置参数,
                    连接配置,
                    1 if 启用状态 else 0,
                ),
            )

            if 结果:
                工具id = 结果[0]["id"]
                工具数据层日志器.info(f"添加MCP工具成功: {工具名称} (ID: {工具id})")
                return 工具id

            return None

        except Exception as e:
            工具数据层日志器.error(f"添加MCP工具失败 ({工具名称}): {str(e)}")
            return None

    async def 更新MCP工具状态(self, 工具id: int, 启用状态: bool) -> bool:
        """更新MCP工具启用状态"""
        try:
            更新SQL = """
            UPDATE langchain_mcp工具表
            SET 启用状态 = $1, 更新时间 = CURRENT_TIMESTAMP
            WHERE id = $2
            """

            await self.数据库操作.执行更新(更新SQL, (1 if 启用状态 else 0, 工具id))
            工具数据层日志器.info(
                f"更新MCP工具状态成功: ID {工具id} -> {'启用' if 启用状态 else '禁用'}"
            )
            return True

        except Exception as e:
            工具数据层日志器.error(f"更新MCP工具状态失败 (ID: {工具id}): {str(e)}")
            return False

    async def 获取工具统计信息(
        self, 工具名称: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取工具统计信息 - 由于统计字段已删除，返回基本工具信息"""
        try:
            if 工具名称:
                查询SQL = """
                SELECT 工具名称, 工具描述, 超时时间, 创建时间, 更新时间
                FROM langchain_工具配置表
                WHERE 工具名称 = $1
                """
                参数 = [工具名称]
            else:
                查询SQL = """
                SELECT 工具名称, 工具描述, 超时时间, 创建时间, 更新时间
                FROM langchain_工具配置表
                ORDER BY 创建时间 DESC
                """
                参数 = []

            结果 = await self.数据库操作.执行查询(
                查询SQL, tuple(参数) if 参数 else None
            )

            统计列表 = []
            for 行 in 结果:
                统计数据 = dict(行)
                # 添加默认统计值
                统计数据["使用次数"] = 0
                统计数据["成功次数"] = 0
                统计数据["失败次数"] = 0
                统计数据["成功率"] = 0.0
                统计列表.append(统计数据)

            return 统计列表

        except Exception as e:
            工具数据层日志器.error(f"获取工具统计信息失败: {str(e)}")
            return []

    async def 批量保存智能体工具关联(
        self, 智能体id: int, 工具列表: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """批量保存智能体工具关联 - 事务安全的工具关联保存接口"""
        try:
            工具数据层日志器.info(
                f"开始批量保存智能体工具关联: 智能体{智能体id}, {len(工具列表)}个工具"
            )

            # 准备批量插入数据
            批量插入数据 = []
            成功数量 = 0
            失败数量 = 0
            失败详情 = []

            # 预处理工具列表
            for 工具配置 in 工具列表:
                工具名称 = 工具配置.get("工具名称") or 工具配置.get("name")
                启用状态 = 工具配置.get("enabled", True)
                工具参数 = 工具配置.get("工具配置", {})

                if not 工具名称:
                    失败数量 += 1
                    失败详情.append({"工具": "未知", "原因": "工具名称为空"})
                    continue

                # 只保存启用的工具
                if not 启用状态:
                    工具数据层日志器.debug(f"跳过未启用工具: {工具名称}")
                    continue

                # 准备插入数据
                批量插入数据.append(
                    {
                        "智能体id": 智能体id,
                        "工具名称": 工具名称,
                        "工具配置": 工具参数,
                        "启用状态": 启用状态,
                    }
                )

            # 使用现有的事务方法进行批量操作
            事务SQL列表 = []
            事务参数列表 = []

            # 添加删除现有关联的SQL
            删除SQL = "DELETE FROM langchain_智能体工具关联表 WHERE langchain_智能体配置表id = $1"
            事务SQL列表.append(删除SQL)
            事务参数列表.append((智能体id,))

            # 添加批量插入的SQL
            if 批量插入数据:
                插入SQL = """
                INSERT INTO langchain_智能体工具关联表
                (langchain_智能体配置表id, 工具名称, 工具配置, 启用状态, 创建时间)
                VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
                """

                for 数据 in 批量插入数据:
                    事务SQL列表.append(插入SQL)
                    事务参数列表.append(
                        (
                            数据["智能体id"],
                            数据["工具名称"],
                            json.dumps(数据["工具配置"], ensure_ascii=False)
                            if 数据["工具配置"]
                            else None,
                            1 if 数据["启用状态"] else 0,
                        )
                    )

            # 先删除现有关联
            await self.数据库操作.执行更新(事务SQL列表[0], 事务参数列表[0])

            # 然后批量插入新关联
            成功数量 = 0
            失败数量 = 0
            失败详情 = []

            for i, sql in enumerate(事务SQL列表[1:], 1):
                try:
                    await self.数据库操作.执行更新(sql, 事务参数列表[i])
                    成功数量 += 1
                except Exception as e:
                    失败数量 += 1
                    失败详情.append(
                        {"工具": 批量插入数据[i - 1]["工具名称"], "原因": str(e)}
                    )

            工具数据层日志器.info(f"批量操作完成: 删除现有关联，插入{成功数量}个新关联")

            工具数据层日志器.info(
                f"✅ 批量保存完成: 成功{成功数量}个, 失败{失败数量}个"
            )

            return {
                "success": True,
                "data": {
                    "智能体id": 智能体id,
                    "总数量": len(工具列表),
                    "成功数量": 成功数量,
                    "失败数量": 失败数量,
                    "失败详情": 失败详情,
                    "保存时间": datetime.now().isoformat(),
                },
            }

        except Exception as e:
            工具数据层日志器.error(
                f"批量保存智能体工具关联异常 (智能体{智能体id}): {str(e)}"
            )
            return {"success": False, "error": f"批量保存失败: {str(e)}"}


# 创建全局实例
LangChain工具数据层实例 = LangChain工具数据层()
