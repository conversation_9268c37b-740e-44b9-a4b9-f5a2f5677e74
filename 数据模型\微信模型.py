from datetime import datetime
from enum import Enum
from typing import Optional, Union

from pydantic import BaseModel, Field, field_validator

# 导入统一日志系统

# ================== 排序相关枚举 ==================


class 好友排序字段(str, Enum):
    """
    好友列表排序字段枚举
    """

    对方最后发消息时间 = "对方最后发消息时间"
    我方最后发消息时间 = "我方最后发消息时间"
    入库时间 = "入库时间"


class 排序方向(str, Enum):
    """
    排序方向枚举
    """

    ASC = "ASC"  # 升序
    DESC = "DESC"  # 降序


# ================== 微信好友下次沟通时间相关模型 ==================


class 微信好友下次沟通时间查询模型(BaseModel):
    """
    微信好友下次沟通时间查询请求模型

    用于查询微信好友的下次沟通时间列表

    Attributes:
        我方微信号id (Optional[int]): 指定的微信号ID，如果不指定则获取用户所有微信号的好友
        页码 (int): 页码，默认为1
        每页条数 (int): 每页显示条数，默认为20，最大100
    """

    我方微信号id: Optional[int] = Field(
        None, description="指定的微信号ID，如果不指定则获取用户所有微信号的好友"
    )
    页码: int = Field(1, ge=1, description="页码，必须大于0")
    每页条数: int = Field(20, ge=1, le=100, description="每页显示条数，范围1-100")


# ================== 原有模型 ==================


class 对接状态保存模型(BaseModel):
    """
    微信对接状态保存模型

    用于保存和更新微信好友的产品对接进度状态信息

    Attributes:
        我方微信号ID (int): 我方微信号在数据库中的ID
        对方微信号ID (int): 对方微信号在数据库中的ID
        合作产品ID (int): 合作产品ID
        好友状态 (Optional[int]): 好友状态 (0-普通、1-已添加、-1-已删除)
        回复状态 (Optional[int]): 回复状态 (0-未回复、1-已回复)
        意向状态 (Optional[int]): 意向状态 (-1:无意向, 0:未沟通, 1:有意向)
        样品状态 (Optional[int]): 样品状态 (0:初始, -1:不需要, 1:申样, -2:申样被拒, 2:申样通过, 3:已出单, -3:出单异常, 4:到样, -4:到样异常, 5:主播已取样, -5:主播取样失败)
        排期状态 (Optional[int]): 排期状态 (1:模糊排期, 2:明确排期)
        排期开始时间 (Optional[datetime]): 排期开始时间
        排期结束时间 (Optional[datetime]): 排期结束时间
        开播状态 (Optional[int]): 开播状态 (0:未开播, 记录开播次数(0-未开播))
        销售额 (Optional[str]): 销售额
    """

    我方微信号ID: int = Field(..., description="我方微信号在数据库中的ID")
    对方微信号ID: int = Field(..., description="对方微信号在数据库中的ID")
    合作产品ID: int = Field(..., description="合作产品ID")
    好友状态: Optional[int] = Field(
        None, description="好友状态 (0-普通、1-已添加、-1-已删除)"
    )
    回复状态: Optional[int] = Field(None, description="回复状态 (0-未回复、1-已回复)")
    意向状态: Optional[int] = Field(
        None, description="意向状态 (-1:无意向, 0:未沟通, 1:有意向)"
    )
    样品状态: Optional[int] = Field(
        None,
        description="样品状态 (0:初始, -1:不需要, 1:申样, -2:申样被拒, 2:申样通过, 3:已出单, -3:出单异常, 4:到样, -4:到样异常, 5:主播已取样, -5:主播取样失败)",
    )
    排期状态: Optional[int] = Field(
        None, description="排期状态 (1:模糊排期, 2:明确排期)"
    )
    排期开始时间: Optional[datetime] = Field(None, description="排期开始时间")
    排期结束时间: Optional[datetime] = Field(None, description="排期结束时间")
    开播状态: Optional[int] = Field(
        None, description="开播状态 (0:未开播, 记录开播次数(0-未开播))"
    )
    销售额: Optional[str] = Field(None, description="销售额")


class 对接状态查询模型(BaseModel):
    """
    微信对接状态查询模型

    用于查询指定微信好友和产品的对接进度状态

    Attributes:
        我方微信号 (str): 我方微信号
        对方微信号 (str): 对方微信号
        合作产品ID (int): 合作产品ID
    """

    我方微信号: str = Field(..., description="我方微信号")
    对方微信号: str = Field(..., description="对方微信号")
    合作产品ID: int = Field(..., description="合作产品ID")


class 微信id查询模型(BaseModel):
    """
    微信id查询模型

    用于根据微信号查询或创建微信账号记录

    Attributes:
        微信号 (str): 微信号
    """

    微信号: str = Field(..., description="微信号")


class 微信好友添加模型(BaseModel):
    """
    微信好友添加模型

    用于添加微信好友关系记录

    Attributes:
        我方微信号 (str): 我方微信号
        对方微信号 (str): 对方微信号
        对方微信头像 (Optional[str]): 对方微信头像，base64格式
    """

    我方微信号: str = Field(..., description="我方微信号")
    对方微信号: str = Field(..., description="对方微信号")
    对方微信头像: Optional[str] = Field(None, description="对方微信头像，base64格式")


class 微信好友查询模型(BaseModel):
    """
    微信好友查询模型

    用于查询指定的微信好友关系信息

    Attributes:
        我方微信号ID (int): 我方微信号在数据库中的ID
        好友识别ID (int): 微信好友关系的识别ID（针对我方微信号唯一）
    """

    我方微信号ID: int = Field(..., description="我方微信号在数据库中的ID")
    好友识别ID: int = Field(
        ..., description="微信好友关系的识别ID（针对我方微信号唯一）"
    )


# ================== 扩展微信管理数据模型 ==================


# 微信账号管理模型
class 微信账号列表请求模型(BaseModel):
    """
    微信账号列表请求模型

    用于获取微信账号列表的分页查询请求

    Attributes:
        页码 (int): 页码，默认为1
        每页条数 (int): 每页条数，默认为20
        状态 (Optional[str]): 账号状态筛选条件
        关键词 (Optional[str]): 搜索关键词
    """

    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    状态: Optional[str] = Field(None, description="账号状态筛选")
    关键词: Optional[str] = Field(None, description="搜索关键词")


class 微信账号更新模型(BaseModel):
    """
    微信账号更新模型

    用于更新现有微信账号的信息

    Attributes:
        账号ID (int): 账号ID
        昵称 (Optional[str]): 微信昵称
        微信号 (Optional[str]): 微信号
        绑定手机号 (Optional[str]): 绑定手机号
        头像 (Optional[str]): 头像URL
        备注 (Optional[str]): 备注信息
        状态 (Optional[str]): 账号状态
    """

    账号ID: int = Field(..., description="账号ID")
    昵称: Optional[str] = Field(None, description="微信昵称")
    微信号: Optional[str] = Field(None, description="微信号")
    绑定手机号: Optional[str] = Field(None, description="绑定手机号")
    头像: Optional[str] = Field(None, description="头像URL")
    备注: Optional[str] = Field(None, description="备注")
    状态: Optional[str] = Field(None, description="账号状态")


class 微信账号删除模型(BaseModel):
    """
    微信账号删除模型

    用于删除微信账号记录

    Attributes:
        账号ID (int): 账号ID
    """

    账号ID: int = Field(..., description="账号ID")


# 微信好友管理模型
class 微信好友列表请求模型(BaseModel):
    """
    微信好友列表请求模型

    用于获取微信好友列表的分页查询请求

    Attributes:
        微信账号ID (Optional[int]): 微信账号ID
        页码 (int): 页码，默认为1
        每页条数 (int): 每页条数，默认为20
        好友类型 (Optional[str]): 好友类型筛选条件
        关键词 (Optional[str]): 搜索关键词
    """

    微信账号ID: Optional[int] = Field(None, description="微信账号ID")
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    好友类型: Optional[str] = Field(None, description="好友类型筛选")
    关键词: Optional[str] = Field(None, description="搜索关键词")


class 微信好友更新模型(BaseModel):
    """
    微信好友更新模型

    用于更新现有微信好友的信息，通过我方微信id和识别ID定位记录

    Attributes:
        我方微信id (int): 我方微信号ID
        识别id (int): 识别ID，用于定位具体的好友记录
        是否失效 (Optional[int]): 是否失效状态
        发送请求时间 (Optional[Union[datetime, int]]): 发送好友请求的时间，支持datetime对象或10位时间戳
        好友入库时间 (Optional[Union[datetime, int]]): 好友入库时间，支持datetime对象或10位时间戳
        我方最后一条消息发送时间 (Optional[Union[datetime, int]]): 我方最后一条消息发送时间，支持datetime对象或10位时间戳
        对方最后一条消息发送时间 (Optional[Union[datetime, int]]): 对方最后一条消息发送时间，支持datetime对象或10位时间戳
        备注 (Optional[str]): 备注信息
    """

    我方微信id: int = Field(..., description="我方微信号ID")
    识别id: int = Field(..., description="识别ID，用于定位具体的好友记录")
    是否失效: Optional[int] = Field(None, description="是否失效状态")
    发送请求时间: Optional[Union[datetime, int]] = Field(
        None, description="发送好友请求的时间，支持datetime对象或10位时间戳"
    )
    好友入库时间: Optional[Union[datetime, int]] = Field(
        None, description="好友入库时间，支持datetime对象或10位时间戳"
    )
    我方最后一条消息发送时间: Optional[Union[datetime, int]] = Field(
        None, description="我方最后一条消息发送时间，支持datetime对象或10位时间戳"
    )
    对方最后一条消息发送时间: Optional[Union[datetime, int]] = Field(
        None, description="对方最后一条消息发送时间，支持datetime对象或10位时间戳"
    )
    备注: Optional[str] = Field(
        None, max_length=500, description="备注信息，最多500个字符"
    )

    @field_validator(
        "发送请求时间",
        "好友入库时间",
        "我方最后一条消息发送时间",
        "对方最后一条消息发送时间",
        mode="before",
    )
    @classmethod
    def 转换时间戳(cls, v):
        """将10位时间戳转换为datetime对象，过滤无效时间戳"""
        if v is None:
            return v
        if isinstance(v, int):
            # 过滤无效的时间戳：0或负数
            if v <= 0:
                return None

            # 过滤不合理的时间戳（太小或太大的值）
            # 10位时间戳范围：2000年到2100年
            min_timestamp = 946684800  # 2000-01-01 00:00:00
            max_timestamp = 4102444800  # 2100-01-01 00:00:00

            try:
                # 处理10位时间戳
                if len(str(v)) == 10:
                    if min_timestamp <= v <= max_timestamp:
                        return datetime.fromtimestamp(v)
                    else:
                        return None
                # 如果是13位时间戳，转换为10位
                elif len(str(v)) == 13:
                    timestamp_10 = v // 1000
                    if min_timestamp <= timestamp_10 <= max_timestamp:
                        return datetime.fromtimestamp(timestamp_10)
                    else:
                        return None
                else:
                    # 时间戳位数不对，返回None
                    return None
            except (ValueError, OverflowError):
                # 时间戳转换失败，返回None
                return None
        return v


class 微信头像更新请求模型(BaseModel):
    """
    微信头像更新请求模型

    用于更新微信信息表中的头像字段

    Attributes:
        微信号 (str): 微信号，用于标识要更新的微信账号
        微信头像 (str): base64编码的头像数据，包含完整的data URL格式
    """

    微信号: str = Field(
        ...,
        description="微信号，用于标识要更新的微信账号",
        min_length=1,
        max_length=255,
    )
    微信头像: str = Field(
        ..., description="base64编码的头像数据，支持data:image格式", min_length=1
    )

    @field_validator("微信头像")
    @classmethod
    def 验证头像格式(cls, v):
        """验证头像数据格式和大小"""
        if not v:
            raise ValueError("头像数据不能为空")

        # 检查是否为data URL格式
        if v.startswith("data:image/"):
            # 提取base64部分
            try:
                header, data = v.split(",", 1)
                # 验证图片格式
                if not any(
                    fmt in header.lower()
                    for fmt in ["jpeg", "jpg", "png", "gif", "webp"]
                ):
                    raise ValueError("不支持的图片格式，仅支持 JPEG、PNG、GIF、WebP")

                # 验证base64格式
                import base64

                base64.b64decode(data)

                # 检查文件大小（base64编码后的大小约为原文件的4/3）
                # 限制为2MB，base64编码后约为2.7MB
                if len(data) > 2 * 1024 * 1024 * 4 / 3:
                    raise ValueError("头像文件过大，请选择小于2MB的图片")

            except (ValueError, Exception) as e:
                if "头像文件过大" in str(e) or "不支持的图片格式" in str(e):
                    raise e
                raise ValueError("无效的base64图片数据格式")
        else:
            # 如果不是data URL格式，尝试直接验证base64
            try:
                import base64

                base64.b64decode(v)

                # 检查文件大小
                if len(v) > 2 * 1024 * 1024 * 4 / 3:
                    raise ValueError("头像文件过大，请选择小于2MB的图片")

            except Exception:
                raise ValueError("无效的base64数据格式")

        return v


class 微信头像更新响应模型(BaseModel):
    """
    微信头像更新响应模型

    Attributes:
        微信号 (str): 更新的微信号
        更新时间 (str): 更新时间
        头像大小 (int): 头像数据大小（字节）
    """

    微信号: str = Field(..., description="更新的微信号")
    更新时间: str = Field(..., description="更新时间")
    头像大小: int = Field(..., description="头像数据大小（字节）")


class 微信好友删除模型(BaseModel):
    """
    微信好友删除模型

    用于删除微信好友记录

    Attributes:
        好友ID (int): 好友ID（对应数据库中的对方微信号id）
    """

    好友ID: int = Field(..., description="好友ID（对应数据库中的对方微信号id）")


# 产品对接进度管理模型
class 产品对接进度列表请求模型(BaseModel):
    """
    产品对接进度列表请求模型

    用于获取产品对接进度列表的分页查询请求

    Attributes:
        微信id (Optional[int]): 我方微信号ID，用于筛选特定微信账号的进度
        页码 (int): 页码，默认为1
        每页条数 (int): 每页条数，默认为20
        状态 (Optional[str]): 进度状态筛选条件
        用户id (Optional[int]): 用户id筛选条件
        产品名称 (Optional[str]): 产品名称搜索关键词
    """

    微信id: Optional[int] = Field(
        None, description="我方微信号ID，用于筛选特定微信账号的进度"
    )
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    状态: Optional[str] = Field(None, description="进度状态筛选")
    用户id: Optional[int] = Field(None, description="用户id筛选")
    产品名称: Optional[str] = Field(None, description="产品名称搜索")


class 产品对接进度创建模型(BaseModel):
    """
    产品对接进度创建模型

    用于创建新的产品对接进度记录

    Attributes:
        用户id (int): 用户id
        产品名称 (str): 产品名称
        产品链接 (Optional[str]): 产品链接
        产品描述 (Optional[str]): 产品描述
        目标平台 (Optional[str]): 目标平台
        备注 (Optional[str]): 备注信息
    """

    用户id: int = Field(..., description="用户id")
    产品名称: str = Field(..., description="产品名称")
    产品链接: Optional[str] = Field(None, description="产品链接")
    产品描述: Optional[str] = Field(None, description="产品描述")
    目标平台: Optional[str] = Field(None, description="目标平台")
    备注: Optional[str] = Field(None, description="备注")


class 产品对接进度更新模型(BaseModel):
    """
    产品对接进度更新模型

    用于更新现有产品对接进度记录

    Attributes:
        进度ID (int): 进度ID
        产品名称 (Optional[str]): 产品名称
        产品链接 (Optional[str]): 产品链接
        产品描述 (Optional[str]): 产品描述
        目标平台 (Optional[str]): 目标平台
        状态 (Optional[str]): 进度状态
        进度百分比 (Optional[int]): 进度百分比
        备注 (Optional[str]): 备注信息
    """

    进度ID: int = Field(..., description="进度ID")
    产品名称: Optional[str] = Field(None, description="产品名称")
    产品链接: Optional[str] = Field(None, description="产品链接")
    产品描述: Optional[str] = Field(None, description="产品描述")
    目标平台: Optional[str] = Field(None, description="目标平台")
    状态: Optional[str] = Field(None, description="进度状态")
    进度百分比: Optional[int] = Field(None, description="进度百分比")
    备注: Optional[str] = Field(None, description="备注")


class 产品对接进度删除模型(BaseModel):
    """
    产品对接进度删除模型

    用于删除产品对接进度记录

    Attributes:
        进度ID (int): 进度ID
    """

    进度ID: int = Field(..., description="进度ID")


# ================== 用户微信关联管理模型 ==================


class 用户微信好友查询模型(BaseModel):
    """
    用户微信好友查询模型

    用于查询用户的微信好友列表

    Attributes:
        页码 (int): 页码，默认为1
        每页条数 (int): 每页条数，默认为20
        关键词 (Optional[str]): 搜索关键词（好友昵称/微信号）
        好友类型 (Optional[str]): 好友类型筛选条件
        排序字段 (Optional[好友排序字段]): 排序字段，支持按消息时间和入库时间排序
        排序方向 (Optional[排序方向]): 排序方向，升序或降序
    """

    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    关键词: Optional[str] = Field(None, description="搜索关键词（好友昵称/微信号）")
    好友类型: Optional[str] = Field(None, description="好友类型筛选")
    排序字段: Optional[str] = Field(None, description="排序字段")
    排序方向: Optional[str] = Field("DESC", description="排序方向，默认降序")


class 微信账号绑定模型(BaseModel):
    """
    微信账号绑定模型 - 扩展版本

    用于用户绑定微信账号，包含格式验证和安全检查
    支持更多可选字段，完全兼容原添加微信账号的功能

    Attributes:
        微信号 (str): 要绑定的微信号，3-30个字符
        备注 (Optional[str]): 绑定备注，最多100个字符
        昵称 (Optional[str]): 微信昵称，可选
        绑定手机号 (Optional[str]): 绑定手机号，可选
        头像 (Optional[str]): 头像URL，可选
    """

    微信号: str = Field(
        ...,
        min_length=3,
        max_length=30,
        description="要绑定的微信号，3-30个字符",
        pattern="^[a-zA-Z0-9_-]+$",
    )
    备注: Optional[str] = Field(
        None, max_length=100, description="绑定备注，最多100个字符"
    )
    # 新增可选字段，兼容原添加微信账号功能
    昵称: Optional[str] = Field(None, description="微信昵称")
    绑定手机号: Optional[str] = Field(None, description="绑定手机号")
    头像: Optional[str] = Field(None, description="头像URL")


class 微信账号解绑模型(BaseModel):
    """
    微信账号解绑模型

    用于用户解绑微信账号

    Attributes:
        微信id (int): 要解绑的微信id
    """

    微信id: int = Field(..., description="要解绑的微信id")


class 指定微信好友查询模型(BaseModel):
    """
    指定微信好友查询模型

    用于查询指定微信账号的好友列表

    Attributes:
        微信id (int): 微信账号ID
        页码 (int): 页码，默认为1
        每页条数 (int): 每页条数，默认为20
        关键词 (Optional[str]): 搜索关键词（好友昵称/微信号）
    """

    微信id: int = Field(..., description="微信账号ID")
    页码: int = Field(1, description="页码")
    每页条数: int = Field(20, description="每页条数")
    关键词: Optional[str] = Field(None, description="搜索关键词（好友昵称/微信号）")


class 用户微信好友请求查询模型(BaseModel):
    """
    用户微信好友请求查询模型

    用于查询用户的微信好友请求状态，包括已有的请求和需要补充的达人联系方式

    Attributes:
        用户id (int): 用户id，用于查询该用户的微信好友请求状态
    """

    用户id: int = Field(..., description="用户id，用于查询该用户的微信好友请求状态")


class 用户微信好友请求响应模型(BaseModel):
    """
    用户微信好友请求响应模型

    用于返回用户微信好友请求状态的查询结果

    Attributes:
        状态类型 (str): 状态类型 ("有好友请求" | "需要添加好友请求" | "需要补充达人联系方式")
        微信添加记录ID (Optional[int]): 用户_联系方式_微信添加记录表的ID
        达人昵称 (Optional[str]): 达人昵称
        联系方式 (Optional[str]): 联系方式内容
        联系方式类型 (Optional[str]): 联系方式类型
        用户达人补充信息表ID (Optional[int]): 用户达人补充信息表ID
        用户达人关联表ID (Optional[int]): 用户达人关联表ID
        好友请求状态 (Optional[str]): 好友请求状态
    """

    状态类型: str = Field(..., description="状态类型")
    微信添加记录ID: Optional[int] = Field(
        None, description="用户_联系方式_微信添加记录表的ID"
    )
    达人昵称: Optional[str] = Field(None, description="达人昵称")
    联系方式: Optional[str] = Field(None, description="联系方式内容")
    联系方式类型: Optional[str] = Field(None, description="联系方式类型")
    用户达人补充信息表ID: Optional[int] = Field(
        None, description="用户达人补充信息表ID"
    )
    用户达人关联表ID: Optional[int] = Field(None, description="用户达人关联表ID")
    好友请求状态: Optional[str] = Field(None, description="好友请求状态")


# ================== 超简化微信自动添加好友系统模型 ==================


class 微信自动添加好友请求模型(BaseModel):
    """
    微信自动添加好友请求模型

    复用 friend-request-status 的逻辑，增加微信账号参数

    Attributes:
        微信信息表id (int): 用户自己的微信账号ID
    """

    微信信息表id: int = Field(..., description="用户自己的微信账号ID")


class 更新微信添加状态请求模型(BaseModel):
    """
    更新微信添加状态请求模型

    根据记录ID更新添加状态

    Attributes:
        记录id (int): 用户_联系方式_微信添加记录表的记录ID
        好友请求状态 (str): 更新后的好友请求状态
        备注 (Optional[str]): 备注信息
    """

    记录id: int = Field(..., description="用户_联系方式_微信添加记录表的记录ID")
    好友请求状态: str = Field(..., description="更新后的好友请求状态")
    备注: Optional[str] = Field(None, max_length=500, description="备注信息")


# ================== 用户微信配置相关模型 ==================


class 用户微信添加配置请求模型(BaseModel):
    """
    用户微信添加配置请求模型

    用户为指定微信号设置个性化添加参数

    Attributes:
        微信信息表id (int): 微信信息表ID
        配置名称 (Optional[str]): 配置名称
        每日最大添加次数 (int): 每日最大添加好友次数
        最小添加间隔分钟 (int): 最小添加间隔分钟
        最大添加间隔分钟 (int): 最大添加间隔分钟
        工作开始时间 (str): 工作开始时间 (HH:MM格式)
        工作结束时间 (str): 工作结束时间 (HH:MM格式)
        午休开始时间 (Optional[str]): 午休开始时间
        午休结束时间 (Optional[str]): 午休结束时间
        是否启用午休 (bool): 是否启用午休
        周末是否添加 (bool): 周末是否添加好友
        周末每日最大添加次数 (Optional[int]): 周末每日最大添加次数
        连续添加次数上限 (int): 连续添加多少次后长时间休息
        批次休息最小分钟 (int): 批次间休息最小分钟
        批次休息最大分钟 (int): 批次间休息最大分钟
        随机延迟最小分钟 (int): 随机延迟最小分钟
        随机延迟最大分钟 (int): 随机延迟最大分钟
        成功率模拟概率 (float): 成功率模拟概率
        每小时最大添加次数 (int): 每小时最大添加次数
        异常检测暂停分钟 (int): 异常检测暂停分钟
    """

    微信信息表id: int = Field(..., description="微信信息表ID")
    配置名称: Optional[str] = Field("默认配置", max_length=100, description="配置名称")

    # 基础限制参数
    每日最大添加次数: int = Field(20, ge=1, le=50, description="每日最大添加好友次数")
    最小添加间隔分钟: int = Field(15, ge=1, le=120, description="最小添加间隔分钟")
    最大添加间隔分钟: int = Field(25, ge=10, le=180, description="最大添加间隔分钟")

    # 工作时间段配置
    工作开始时间: str = Field("09:00:00", description="工作开始时间(HH:mm:ss格式)")
    工作结束时间: str = Field("22:00:00", description="工作结束时间(HH:mm:ss格式)")
    午休开始时间: Optional[str] = Field(
        "12:00:00", description="午休开始时间(HH:mm:ss格式)"
    )
    午休结束时间: Optional[str] = Field(
        "14:00:00", description="午休结束时间(HH:mm:ss格式)"
    )
    是否启用午休: int = Field(1, ge=0, le=1, description="是否启用午休(0-否，1-是)")

    # 周末配置
    周末是否添加: int = Field(0, ge=0, le=1, description="周末是否添加(0-否，1-是)")
    周末每日最大添加次数: Optional[int] = Field(
        10, ge=1, le=30, description="周末每日最大添加次数"
    )

    # 批次配置
    连续添加次数上限: int = Field(
        5, ge=2, le=15, description="连续添加多少次后需要长时间休息"
    )
    批次休息最小分钟: int = Field(60, ge=30, le=300, description="批次间休息最小分钟")
    批次休息最大分钟: int = Field(120, ge=60, le=480, description="批次间休息最大分钟")

    # 随机化参数
    随机延迟最小分钟: int = Field(0, ge=0, le=30, description="随机延迟最小分钟")
    随机延迟最大分钟: int = Field(5, ge=0, le=60, description="随机延迟最大分钟")
    成功率模拟概率: float = Field(0.95, ge=0.1, le=1.0, description="成功率模拟概率")

    # 安全防护参数
    每小时最大添加次数: int = Field(4, ge=1, le=100, description="每小时最大添加次数")
    异常检测暂停分钟: int = Field(30, ge=10, le=120, description="异常检测暂停分钟")

    # 消息模板参数
    验证消息模板: Optional[str] = Field(
        "你好，我是{达人昵称}的朋友，想和你交流一下",
        max_length=500,
        description="好友验证消息模板，支持变量替换",
    )
    好友备注模板: Optional[str] = Field(
        "{达人昵称}-{联系方式类型}",
        max_length=200,
        description="好友备注模板，支持变量替换",
    )


class 用户微信添加配置响应模型(BaseModel):
    """
    用户微信添加配置响应模型

    返回用户微信添加配置信息
    """

    id: int = Field(..., description="配置id")
    微信信息表id: int = Field(..., description="微信信息表ID")
    配置名称: str = Field(..., description="配置名称")

    # 基础限制参数
    每日最大添加次数: int = Field(..., description="每日最大添加好友次数")
    最小添加间隔分钟: int = Field(..., description="最小添加间隔分钟")
    最大添加间隔分钟: int = Field(..., description="最大添加间隔分钟")

    # 工作时间段配置
    工作开始时间: str = Field(..., description="工作开始时间")
    工作结束时间: str = Field(..., description="工作结束时间")
    午休开始时间: Optional[str] = Field(None, description="午休开始时间")
    午休结束时间: Optional[str] = Field(None, description="午休结束时间")
    是否启用午休: bool = Field(..., description="是否启用午休")

    # 周末配置
    周末是否添加: bool = Field(..., description="周末是否添加好友")
    周末每日最大添加次数: Optional[int] = Field(
        None, description="周末每日最大添加次数"
    )

    # 批次配置
    连续添加次数上限: int = Field(..., description="连续添加次数上限")
    批次休息最小分钟: int = Field(..., description="批次休息最小分钟")
    批次休息最大分钟: int = Field(..., description="批次休息最大分钟")

    # 随机化参数
    随机延迟最小分钟: int = Field(..., description="随机延迟最小分钟")
    随机延迟最大分钟: int = Field(..., description="随机延迟最大分钟")
    成功率模拟概率: float = Field(..., description="成功率模拟概率")

    # 安全防护参数
    每小时最大添加次数: int = Field(..., description="每小时最大添加次数")
    异常检测暂停分钟: int = Field(..., description="异常检测暂停分钟")

    # 消息模板参数
    验证消息模板: str = Field(..., description="好友验证消息模板，支持变量替换")
    好友备注模板: str = Field(..., description="好友备注模板，支持变量替换")

    # 状态信息
    创建时间: datetime = Field(..., description="创建时间")
    更新时间: datetime = Field(..., description="更新时间")


class 微信自动添加状态响应模型(BaseModel):
    """
    微信自动添加状态响应模型

    返回基于用户自定义配置的智能添加状态信息

    Attributes:
        是否可立即执行 (bool): 当前是否可以立即执行添加操作
        当日已添加计数 (int): 当日累计已添加好友次数
        当日剩余配额 (int): 当日剩余可添加次数配额
        连续操作计数 (int): 当前连续添加操作次数
        需要长时间休息 (bool): 是否需要执行长时间批次休息
        计划执行时间 (Optional[datetime]): 系统计算的下次计划执行时间
        实际添加时间 (Optional[datetime]): 上次实际执行添加的时间
        限制原因说明 (str): 当前限制或等待的具体原因说明
        验证消息 (str): 根据用户配置生成的好友验证消息
        好友备注 (str): 根据用户配置生成的好友备注
        当前生效配置 (dict): 当前微信号生效的配置参数概览
        联系方式数据 (list): 需要添加的联系方式列表数据

    注意：执行状态描述信息现在直接放在响应的message字段中
    """

    是否可立即执行: bool = Field(..., description="当前是否可以立即执行添加操作")
    当日已添加计数: int = Field(..., description="当日累计已添加好友次数")
    当日剩余配额: int = Field(..., description="当日剩余可添加次数配额")
    连续操作计数: int = Field(..., description="当前连续添加操作次数")
    需要长时间休息: bool = Field(..., description="是否需要执行长时间批次休息")
    计划执行时间: Optional[datetime] = Field(
        None, description="系统计算的下次计划执行时间"
    )
    实际添加时间: Optional[datetime] = Field(None, description="上次实际执行添加的时间")
    限制原因说明: str = Field(..., description="当前限制或等待的具体原因说明")
    验证消息: str = Field(..., description="根据用户配置生成的好友验证消息")
    好友备注: str = Field(..., description="根据用户配置生成的好友备注")
    当前生效配置: dict = Field(..., description="当前微信号生效的配置参数概览")
    联系方式数据: list = Field(..., description="需要添加的联系方式列表数据")


# 响应模型复用现有的 微信添加状态检查响应模型 和 微信添加历史记录响应模型 即可


class 微信添加历史记录响应模型(BaseModel):
    """
    微信添加历史记录响应模型

    返回微信号的添加好友历史记录

    Attributes:
        记录id (int): 记录ID
        目标联系方式 (str): 目标联系方式
        联系方式类型 (str): 联系方式类型
        验证消息 (str): 验证消息
        操作类型 (str): 操作类型
        操作结果 (str): 操作结果
        执行时间 (datetime): 执行时间
        错误信息 (Optional[str]): 错误信息
    """

    记录id: int = Field(..., description="记录ID")
    目标联系方式: str = Field(..., description="目标联系方式")
    联系方式类型: str = Field(..., description="联系方式类型")
    验证消息: str = Field(..., description="验证消息")
    操作类型: str = Field(..., description="操作类型")
    操作结果: str = Field(..., description="操作结果")
    执行时间: datetime = Field(..., description="执行时间")
    错误信息: Optional[str] = Field(None, description="错误信息")


# ================== 微信自动化配置管理请求模型 ==================


class 配置列表查询请求模型(BaseModel):
    """
    配置列表查询请求模型

    用于查询用户微信自动化配置列表的分页和搜索参数

    Attributes:
        页码 (int): 页码，从1开始
        每页数量 (int): 每页显示的配置数量
        搜索关键词 (Optional[str]): 搜索关键词，用于配置名称模糊搜索
    """

    页码: int = Field(1, ge=1, description="页码，从1开始")
    每页数量: int = Field(10, ge=1, le=100, description="每页显示的配置数量，最大100")
    搜索关键词: Optional[str] = Field(
        "", description="搜索关键词，用于配置名称模糊搜索"
    )


class 更新微信自动化配置请求模型(BaseModel):
    """
    更新微信自动化配置请求模型

    用于更新现有微信自动化配置的所有参数
    继承自用户微信添加配置请求模型，并添加配置id字段

    Attributes:
        配置id (int): 要更新的配置id
        配置名称 (str): 配置名称
        微信信息表id (int): 绑定的微信账号ID
        每日最大添加次数 (int): 每日最大添加次数
        最小添加间隔分钟 (int): 最小添加间隔(分钟)
        最大添加间隔分钟 (int): 最大添加间隔(分钟)
        工作开始时间 (str): 工作开始时间(HH:mm:ss格式)
        工作结束时间 (str): 工作结束时间(HH:mm:ss格式)
        午休开始时间 (Optional[str]): 午休开始时间(HH:mm:ss格式)
        午休结束时间 (Optional[str]): 午休结束时间(HH:mm:ss格式)
        是否启用午休 (int): 是否启用午休(0-否，1-是)
        周末是否添加 (int): 周末是否添加(0-否，1-是)
        周末每日最大添加次数 (Optional[int]): 周末每日最大添加次数
        连续添加次数上限 (int): 连续添加次数上限
        批次休息最小分钟 (int): 批次休息最小分钟
        批次休息最大分钟 (int): 批次休息最大分钟
        随机延迟最小分钟 (int): 随机延迟最小分钟
        随机延迟最大分钟 (int): 随机延迟最大分钟
        成功率模拟概率 (float): 成功率模拟概率(0-1)
        每小时最大添加次数 (int): 每小时最大添加次数
        异常检测暂停分钟 (int): 异常检测暂停分钟
    """

    配置id: int = Field(..., description="要更新的配置id")
    配置名称: str = Field(..., max_length=100, description="配置名称，最大100字符")
    微信信息表id: int = Field(..., description="绑定的微信账号ID")
    每日最大添加次数: int = Field(
        20, ge=1, le=500, description="每日最大添加次数，范围1-500"
    )
    最小添加间隔分钟: int = Field(
        15, ge=1, le=1440, description="最小添加间隔(分钟)，范围1-1440"
    )
    最大添加间隔分钟: int = Field(
        25, ge=1, le=1440, description="最大添加间隔(分钟)，范围1-1440"
    )
    工作开始时间: str = Field("09:00:00", description="工作开始时间(HH:mm:ss格式)")
    工作结束时间: str = Field("22:00:00", description="工作结束时间(HH:mm:ss格式)")
    午休开始时间: Optional[str] = Field(
        "12:00:00", description="午休开始时间(HH:mm:ss格式)"
    )
    午休结束时间: Optional[str] = Field(
        "14:00:00", description="午休结束时间(HH:mm:ss格式)"
    )
    是否启用午休: int = Field(1, ge=0, le=1, description="是否启用午休(0-否，1-是)")
    周末是否添加: int = Field(0, ge=0, le=1, description="周末是否添加(0-否，1-是)")
    周末每日最大添加次数: Optional[int] = Field(
        10, ge=1, le=100, description="周末每日最大添加次数"
    )
    连续添加次数上限: int = Field(
        5, ge=1, le=20, description="连续添加次数上限，范围1-20"
    )
    批次休息最小分钟: int = Field(
        60, ge=1, le=1440, description="批次休息最小分钟，范围1-1440"
    )
    批次休息最大分钟: int = Field(
        120, ge=1, le=1440, description="批次休息最大分钟，范围1-1440"
    )
    随机延迟最小分钟: int = Field(
        0, ge=0, le=60, description="随机延迟最小分钟，范围0-60"
    )
    随机延迟最大分钟: int = Field(
        5, ge=0, le=60, description="随机延迟最大分钟，范围0-60"
    )
    成功率模拟概率: float = Field(
        0.95, ge=0.0, le=1.0, description="成功率模拟概率，范围0-1"
    )
    每小时最大添加次数: int = Field(
        4, ge=1, le=100, description="每小时最大添加次数，范围1-100"
    )
    异常检测暂停分钟: int = Field(
        30, ge=1, le=1440, description="异常检测暂停分钟，范围1-1440"
    )
    验证消息模板: Optional[str] = Field(
        "", max_length=500, description="验证消息模板，最大500字符"
    )
    好友备注模板: Optional[str] = Field(
        "", max_length=100, description="好友备注模板，最大100字符"
    )


class 删除微信自动化配置请求模型(BaseModel):
    """
    删除微信自动化配置请求模型

    用于删除指定的微信自动化配置

    Attributes:
        配置id (int): 要删除的配置id
    """

    配置id: int = Field(..., description="要删除的配置id")


class 获取配置详情请求模型(BaseModel):
    """
    获取配置详情请求模型

    用于获取指定配置的完整详情信息

    Attributes:
        配置id (int): 要获取详情的配置id
    """

    配置id: int = Field(..., description="要获取详情的配置id")


# ================== 自动添加记录管理请求模型 ==================


class 添加记录列表查询请求模型(BaseModel):
    """
    添加记录列表查询请求模型

    用于查询用户微信自动添加好友记录列表的分页和搜索参数

    Attributes:
        页码 (int): 页码，从1开始
        每页数量 (int): 每页显示的记录数量
        搜索关键词 (Optional[str]): 搜索关键词，用于微信号或达人信息模糊搜索
        状态筛选 (Optional[int]): 好友请求状态筛选
        微信账号筛选 (Optional[int]): 微信账号ID筛选
        时间范围 (Optional[list]): 时间范围筛选
    """

    页码: int = Field(1, ge=1, description="页码，从1开始")
    每页数量: int = Field(10, ge=1, le=100, description="每页显示的记录数量，最大100")
    搜索关键词: Optional[str] = Field(
        "", description="搜索关键词，用于微信号或达人信息模糊搜索"
    )
    状态筛选: Optional[int] = Field(None, description="好友请求状态筛选")
    微信账号筛选: Optional[int] = Field(None, description="微信账号ID筛选")
    时间范围: Optional[list] = Field(
        None, description="时间范围筛选，格式：[开始时间, 结束时间]"
    )


class 手动添加记录请求模型(BaseModel):
    """
    手动添加记录请求模型

    用于手动创建微信添加好友记录

    Attributes:
        微信信息表id (int): 绑定的微信账号ID
        目标联系方式 (str): 目标联系方式（微信号、手机号等）
        联系方式类型 (str): 联系方式类型（微信号、手机号、QQ号等）
        计划添加时间 (Optional[datetime]): 计划添加时间
        验证消息 (Optional[str]): 好友验证消息
    """

    微信信息表id: int = Field(..., description="绑定的微信账号ID")
    目标联系方式: str = Field(
        ..., max_length=100, description="目标联系方式，最大100字符"
    )
    联系方式类型: str = Field("微信号", description="联系方式类型")
    计划添加时间: Optional[datetime] = Field(None, description="计划添加时间")
    验证消息: Optional[str] = Field(
        "你好，希望能加个好友", max_length=200, description="好友验证消息，最大200字符"
    )


class 重试记录请求模型(BaseModel):
    """
    重试记录请求模型

    用于重试单个添加记录

    Attributes:
        记录ID (int): 要重试的记录ID
    """

    记录ID: int = Field(..., description="要重试的记录ID")


# ================== 通用记录操作模型 ==================


class 记录ID请求模型(BaseModel):
    """
    单个记录ID请求模型 - 通用基础模型

    用于所有需要单个记录ID的操作（删除、重试、查看等）

    Attributes:
        记录ID (int): 记录ID
    """

    记录ID: int = Field(..., description="记录ID")


class 批量记录IDs请求模型(BaseModel):
    """
    批量记录IDs请求模型 - 通用基础模型

    用于所有需要批量记录ID的操作（批量删除、批量重试等）

    Attributes:
        记录IDs (List[int]): 记录ID列表
    """

    记录IDs: list[int] = Field(
        ..., description="记录ID列表，最多100个", min_length=1, max_length=100
    )


class 导出记录请求模型(BaseModel):
    """
    导出记录请求模型

    用于导出添加记录到Excel文件

    Attributes:
        搜索关键词 (Optional[str]): 搜索关键词
        状态筛选 (Optional[int]): 好友请求状态筛选
        微信账号筛选 (Optional[int]): 微信账号ID筛选
        时间范围 (Optional[list]): 时间范围筛选
    """

    搜索关键词: Optional[str] = Field("", description="搜索关键词")
    状态筛选: Optional[int] = Field(None, description="好友请求状态筛选")
    微信账号筛选: Optional[int] = Field(None, description="微信账号ID筛选")
    时间范围: Optional[list] = Field(None, description="时间范围筛选")


# 记录详情请求模型已合并到通用的 记录ID请求模型
