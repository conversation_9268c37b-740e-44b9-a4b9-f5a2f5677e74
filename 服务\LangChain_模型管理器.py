"""
LangChain模型管理器

功能：
1. AI模型统一管理
2. 模型配置和切换
3. 阿里云通义千问集成
4. 模型性能监控
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Optional

import httpx

# 阿里云SDK
try:
    from openai import OpenAI  # type: ignore

    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

    class OpenAI:  # type: ignore
        pass


# LangChain模型和向量模型
try:
    from langchain_community.embeddings import DashScopeEmbeddings  # type: ignore
    from langchain_core.language_models.chat_models import BaseChatModel  # type: ignore
    from langchain_core.messages import (  # type: ignore
        AIMessage,
        HumanMessage,
        SystemMessage,
    )
    from langchain_openai import ChatOpenAI, OpenAIEmbeddings  # type: ignore

    try:
        from langchain_google_genai import GoogleGenerativeAIEmbeddings  # type: ignore
    except ImportError:
        GoogleGenerativeAIEmbeddings = None
    LANGCHAIN_AVAILABLE = True
    # 使用日志文件夹中的应用日志器
    from 日志 import 应用日志器 as 模型日志器

    模型日志器.info("✅ LangChain库导入成功，启用真实模型调用")


except ImportError as e:
    LANGCHAIN_AVAILABLE = False
    # 使用日志文件夹中的应用日志器
    from 日志 import 应用日志器 as 模型日志器

    模型日志器.critical(f"❌ LangChain库导入失败: {str(e)}，请安装LangChain相关依赖")

    # 如果LangChain不可用，抛出异常而不是提供模拟类
    class ChatOpenAI:  # type: ignore
        def __init__(self, **kwargs):  # type: ignore
            raise ImportError(
                "LangChain库不可用，请安装: pip install langchain langchain-openai langchain-community"
            )

    class BaseChatModel:  # type: ignore
        async def ainvoke(self, 消息列表):  # type: ignore
            raise ImportError("LangChain库不可用，无法使用真实模型")

    class HumanMessage:  # type: ignore
        def __init__(self, content: str = ""):
            raise ImportError("LangChain库不可用")

    class SystemMessage:  # type: ignore
        def __init__(self, content: str = ""):
            raise ImportError("LangChain库不可用")

    class AIMessage:  # type: ignore
        def __init__(self, content: str = ""):
            raise ImportError("LangChain库不可用")

    class OpenAIEmbeddings:
        def __init__(self, **kwargs):
            raise ImportError("LangChain库不可用")

    class DashScopeEmbeddings:
        def __init__(self, **kwargs):
            raise ImportError("LangChain库不可用")

    GoogleGenerativeAIEmbeddings = None


class LangChain模型管理器:
    """LangChain模型管理器"""

    def __init__(self):
        self.模型实例 = {}
        self.模型配置 = {}
        self.向量模型实例 = {}  # 存储向量模型实例
        self.向量模型配置 = {}  # 存储向量模型配置
        self.默认模型 = "qwen-turbo"
        self.默认向量模型 = None
        self.已初始化 = False

        # 阿里云配置
        self.阿里云配置 = {
            "api_key": "",
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
        }

    async def 初始化(self, 阿里云api_key: Optional[str] = None):
        """初始化模型管理器"""
        try:
            # 设置阿里云API密钥
            if 阿里云api_key:
                self.阿里云配置["api_key"] = 阿里云api_key

            # 初始化默认模型配置
            await self._初始化默认模型配置()

            # 初始化向量模型配置
            await self._初始化向量模型配置()

            # 预加载默认模型
            await self._预加载模型(self.默认模型)

            self.已初始化 = True
            模型日志器.info("模型管理器初始化成功")
            return True

        except Exception as e:
            模型日志器.error(f"模型管理器初始化失败: {str(e)}")
            return False

    async def _初始化默认模型配置(self):
        """初始化模型配置 - 优先从数据库加载，回退到默认配置"""
        try:
            # 尝试从数据库加载模型配置
            await self._从数据库加载模型配置()

            # 如果数据库中没有配置，使用默认配置
            if not self.模型配置:
                await self._加载默认模型配置()

            模型日志器.info(f"模型配置初始化完成，共 {len(self.模型配置)} 个模型")

        except Exception as e:
            模型日志器.warning(f"从数据库加载模型配置失败，使用默认配置: {str(e)}")
            await self._加载默认模型配置()

    async def _从数据库加载模型配置(self):
        """从数据库加载模型配置"""
        try:
            # 首先尝试从LangChain_模型配置表加载
            try:
                查询SQL = """
                SELECT id, 模型名称, 显示名称, 提供商, 模型类型,
                       api密钥, api基础url,
                       最大令牌数, 算力消耗
                FROM langchain_模型配置表
                ORDER BY id
                """

                from 数据.LangChain_模型数据层 import LangChain模型数据层实例

                结果 = await LangChain模型数据层实例.数据库连接池.执行查询(查询SQL)

                if 结果:
                    self.模型配置 = {}
                    for 行 in 结果:
                        模型标识 = 行["模型名称"] or f"langchain_model_{行['id']}"

                        # 使用默认模型参数（因为数据库表中没有模型参数字段）
                        模型参数 = {}

                        self.模型配置[模型标识] = {
                            "数据库ID": 行["id"],
                            "名称": 行["显示名称"] or 行["模型名称"],
                            "模型名": 行["模型名称"],
                            "类型": 行["提供商"] or "未知",
                            "模型类型": 行["模型类型"],
                            "API密钥": 行["api密钥"],
                            "API基础URL": 行["api基础url"],
                            "算力消耗": 行["算力消耗"] or 1,
                            "最大令牌数": 行["最大令牌数"] or 4000,
                            "描述": f"{行['提供商']} {行['显示名称']}",
                            "头像": "",
                            "提示词模板": "",
                            "默认参数": {
                                "temperature": 模型参数.get("temperature", 0.7),
                                "max_tokens": 行["最大令牌数"] or 4000,
                                "top_p": 模型参数.get("top_p", 0.9),
                                **模型参数,
                            },
                        }

                    模型日志器.info(
                        f"从langchain_模型配置表加载了 {len(self.模型配置)} 个模型配置"
                    )
                    return

            except Exception as langchain_error:
                模型日志器.warning(
                    f"从langchain_模型配置表加载失败: {str(langchain_error)}"
                )

            # {{ AURA-X: Remove - ai模型表已被删除，移除回退逻辑. Approval: 寸止(ID:1735372800). }}
            # {{ Source: PostgreSQL MCP确认ai模型表不存在 }}
            # ai模型表已被删除，无法回退
            模型日志器.warning("ai模型表已被删除，无法从该表加载模型配置")
            self.模型配置 = {}

        except Exception as e:
            模型日志器.error(f"从数据库加载模型配置失败: {str(e)}")
            raise

    async def _加载默认模型配置(self):
        """加载默认模型配置（回退方案）"""
        self.模型配置 = {
            "qwen-turbo": {
                "数据库ID": None,
                "名称": "通义千问Turbo",
                "类型": "阿里云",
                "模型名": "qwen-turbo",
                "最大令牌": 8000,
                "支持功能": ["对话", "文本生成"],
                "算力消耗": 1,
                "描述": "通义千问轻量版模型",
                "头像": "",
                "提示词模板": "",
                "默认参数": {"temperature": 0.7, "max_tokens": 4000, "top_p": 0.9},
            },
            "qwen-plus": {
                "数据库ID": None,
                "名称": "通义千问Plus",
                "类型": "阿里云",
                "模型名": "qwen-plus",
                "最大令牌": 32000,
                "支持功能": ["对话", "文本生成", "长文本"],
                "算力消耗": 2,
                "描述": "通义千问增强版模型",
                "头像": "",
                "提示词模板": "",
                "默认参数": {"temperature": 0.7, "max_tokens": 8000, "top_p": 0.9},
            },
            "qwen-max": {
                "数据库ID": None,
                "名称": "通义千问Max",
                "类型": "阿里云",
                "模型名": "qwen-max",
                "最大令牌": 8000,
                "支持功能": ["对话", "文本生成", "推理"],
                "算力消耗": 4,
                "描述": "通义千问旗舰版模型",
                "头像": "",
                "提示词模板": "",
                "默认参数": {"temperature": 0.7, "max_tokens": 4000, "top_p": 0.9},
            },
        }

        模型日志器.info(f"加载默认模型配置完成，共 {len(self.模型配置)} 个模型")

    async def _初始化向量模型配置(self):
        """初始化向量模型配置"""
        try:
            # 从数据库加载向量模型配置
            await self._从数据库加载向量模型配置()

            # 设置默认向量模型
            await self._设置默认向量模型()

            模型日志器.info(
                f"向量模型配置初始化完成，共 {len(self.向量模型配置)} 个向量模型"
            )

        except Exception as e:
            模型日志器.warning(f"向量模型配置初始化失败: {str(e)}")

    async def _从数据库加载向量模型配置(self):
        """从数据库加载向量模型配置"""
        try:
            查询SQL = """
            SELECT id, 模型名称, 模型类型, 显示名称, 提供商,
                   api密钥, api基础url
            FROM langchain_模型配置表
            WHERE 模型类型 LIKE '%embedding%'
            ORDER BY id DESC
            """

            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            结果 = await LangChain模型数据层实例.数据库连接池.执行查询(查询SQL)

            self.向量模型配置 = {}
            for 行 in 结果:
                模型名称 = 行["模型名称"]
                self.向量模型配置[模型名称] = {
                    "数据库ID": 行["id"],
                    "模型名称": 模型名称,
                    "模型类型": 行["模型类型"],
                    "显示名称": 行["显示名称"],
                    "提供商": 行["提供商"],
                    "API密钥": 行["api密钥"],
                    "API基础URL": 行["api基础url"],
                }

            模型日志器.info(
                f"从数据库加载向量模型配置成功，共 {len(self.向量模型配置)} 个向量模型"
            )

        except Exception as e:
            模型日志器.warning(f"从数据库加载向量模型配置失败: {str(e)}")
            self.向量模型配置 = {}

    async def _设置默认向量模型(self):
        """设置默认向量模型"""
        if self.向量模型配置:
            # 选择第一个可用的向量模型作为默认
            self.默认向量模型 = list(self.向量模型配置.keys())[0]
            模型日志器.info(f"设置默认向量模型: {self.默认向量模型}")

    async def _预加载模型(self, 模型名称: str):
        """预加载模型实例 - 强制使用真实配置"""
        try:
            if 模型名称 in self.模型实例:
                现有实例 = self.模型实例[模型名称]
                if 现有实例:
                    模型日志器.debug(f"🔄 模型已预加载: {模型名称}")
                    return 现有实例
                else:
                    模型日志器.info(f"🔧 重新加载空模型实例: {模型名称}")

            模型配置 = self.模型配置.get(模型名称)
            if not 模型配置:
                模型日志器.error(f"❌ 未找到模型配置: {模型名称}")
                self.模型实例[模型名称] = None
                return None

            模型日志器.info(f"🚀 开始预加载模型: {模型名称}")
            模型日志器.debug(f"📋 模型配置: {模型配置}")

            # 强制检查依赖库可用性
            if not OPENAI_AVAILABLE:
                错误信息 = "OpenAI库不可用，请安装: pip install openai"
                模型日志器.error(f"❌ {错误信息}")
                raise ImportError(错误信息)

            if not LANGCHAIN_AVAILABLE:
                错误信息 = (
                    "LangChain库不可用，请安装: pip install langchain langchain-openai"
                )
                模型日志器.error(f"❌ {错误信息}")
                raise ImportError(错误信息)

            # 强制检查API密钥配置
            数据库API密钥 = 模型配置.get("API密钥")
            if not 数据库API密钥 or 数据库API密钥 == "None":
                错误信息 = f"模型 {模型名称} 未配置API密钥，请在数据库中配置"
                模型日志器.error(f"❌ {错误信息}")
                raise ValueError(错误信息)

            模型日志器.info(f"✅ API密钥验证通过: {数据库API密钥[:8]}****")

            # 创建真实模型实例
            if 模型配置["类型"] == "阿里云":
                # 使用数据库中的API配置
                API基础URL = (
                    模型配置.get("API基础URL")
                    or "https://dashscope.aliyuncs.com/compatible-mode/v1"
                )
                模型名 = 模型配置["模型名"]
                默认参数 = 模型配置.get("默认参数", {})

                模型日志器.info(f"🔧 配置阿里云模型: {模型名}")
                模型日志器.debug(f"🌐 API基础URL: {API基础URL}")
                模型日志器.debug(f"⚙️ 默认参数: {默认参数}")

                try:
                    模型实例 = ChatOpenAI(
                        model=模型名,  # type: ignore
                        api_key=数据库API密钥,  # type: ignore
                        base_url=API基础URL,  # type: ignore
                        **默认参数,
                    )

                    # 验证模型实例是否创建成功
                    if not 模型实例:
                        raise ValueError("模型实例创建失败，返回None")

                    模型日志器.info(
                        f"✅ 阿里云模型实例创建成功: {type(模型实例).__name__}"
                    )

                    # 测试模型连接（可选）
                    try:
                        # 简单的连接测试
                        # 测试消息 = [HumanMessage(content="测试连接")]  # 暂时不使用
                        # 注意：这里不实际调用，只是验证实例创建
                        模型日志器.debug("🔌 模型实例连接验证通过")
                    except Exception as test_error:
                        模型日志器.warning(
                            f"⚠️ 模型连接测试失败，但实例已创建: {str(test_error)}"
                        )

                except Exception as create_error:
                    错误信息 = f"阿里云模型实例创建失败: {str(create_error)}"
                    模型日志器.error(f"❌ {错误信息}")
                    模型日志器.error(
                        f"🔍 详细信息: 模型={模型名}, API_KEY={数据库API密钥[:8]}****, URL={API基础URL}"
                    )
                    raise RuntimeError(错误信息)

            elif 模型配置["类型"] == "OpenAI":
                # OpenAI官方模型配置
                API基础URL = 模型配置.get("API基础URL", "https://api.openai.com/v1")
                模型名 = 模型配置["模型名"]
                默认参数 = 模型配置.get("默认参数", {})

                模型日志器.info(f"🔧 配置OpenAI模型: {模型名}")

                try:
                    模型实例 = ChatOpenAI(
                        model=模型名,  # type: ignore
                        api_key=数据库API密钥,  # type: ignore
                        base_url=API基础URL,  # type: ignore
                        **默认参数,
                    )

                    模型日志器.info(
                        f"✅ OpenAI模型实例创建成功: {type(模型实例).__name__}"
                    )

                except Exception as create_error:
                    错误信息 = f"OpenAI模型实例创建失败: {str(create_error)}"
                    模型日志器.error(f"❌ {错误信息}")
                    raise RuntimeError(错误信息)

            else:
                错误信息 = f"不支持的模型类型: {模型配置['类型']}"
                模型日志器.error(f"❌ {错误信息}")
                raise ValueError(错误信息)

            # 保存模型实例
            self.模型实例[模型名称] = 模型实例
            模型日志器.info(f"🎉 模型预加载成功: {模型名称}")

            return 模型实例

        except Exception as e:
            模型日志器.error(f"❌ 预加载模型完全失败 {模型名称}: {str(e)}")
            # 不再保存None，直接抛出异常，强制解决配置问题
            raise RuntimeError(f"模型 {模型名称} 预加载失败: {str(e)}")

    async def 获取模型(
        self, 模型名称: Optional[str] = None, **参数
    ) -> Optional[BaseChatModel]:
        """获取模型实例 - 优化版本"""
        try:
            if not self.已初始化:
                模型日志器.info("⚙️ 模型管理器未初始化，正在初始化...")
                await self.初始化()

            使用模型 = 模型名称 or self.默认模型
            模型日志器.debug(f"🔍 获取模型: {使用模型}")

            # 强制重新加载模型（如果不存在或为None）
            if 使用模型 not in self.模型实例 or self.模型实例.get(使用模型) is None:
                模型日志器.info(f"🔄 模型未加载，开始预加载: {使用模型}")
                await self._预加载模型(使用模型)

            模型实例 = self.模型实例.get(使用模型)

            if not 模型实例:
                错误信息 = f"获取模型实例失败: {使用模型}"
                模型日志器.error(f"❌ {错误信息}")
                return None

            # 如果提供了自定义参数，创建新的模型实例
            if 参数:
                模型日志器.info(f"🔧 使用自定义参数创建模型实例: {参数}")
                模型配置 = self.模型配置.get(使用模型, {})
                合并参数 = {**模型配置.get("默认参数", {}), **参数}

                if 模型配置.get("类型") == "阿里云":
                    try:
                        # 使用数据库中的API密钥配置
                        数据库API密钥 = 模型配置.get("API密钥")
                        API基础URL = (
                            模型配置.get("API基础URL")
                            or "https://dashscope.aliyuncs.com/compatible-mode/v1"
                        )

                        if 数据库API密钥:
                            自定义实例 = ChatOpenAI(  # type: ignore
                                model=模型配置["模型名"],  # type: ignore
                                api_key=数据库API密钥,  # type: ignore
                                base_url=API基础URL,  # type: ignore
                                **合并参数,
                            )
                            模型日志器.info("✅ 自定义参数模型实例创建成功")
                            return 自定义实例
                        else:
                            模型日志器.error(f"❌ 数据库中未配置API密钥: {使用模型}")
                            return None
                    except Exception as e:
                        模型日志器.error(f"❌ 自定义参数模型实例创建失败: {str(e)}")
                        return None

            模型日志器.debug(f"✅ 返回已加载的模型实例: {type(模型实例).__name__}")
            return 模型实例

        except Exception as e:
            模型日志器.error(f"❌ 获取模型失败 {模型名称}: {str(e)}")
            return None

    async def 调用模型(
        self,
        模型名称: Optional[str],
        消息列表: List[Dict[str, str]],
        结构化输出模式: Optional[str] = None,
        json_schema: Optional[Dict[str, Any]] = None,
        pydantic_模型类: Optional[Any] = None,
        **参数,
    ) -> Dict[str, Any]:
        """调用模型生成回复 - 支持普通对话和结构化输出"""
        开始时间 = datetime.now()
        使用的模型名称 = 模型名称 or self.默认模型

        # 基础返回结构 - 统一使用中文字段名
        基础返回结构 = {
            "success": False,
            "response": "",
            "error": None,
            "model_name": 使用的模型名称,
            "model_provider": "阿里云",
            "processing_time": 0.0,
            # 代码估算的token数据
            "token_count": 0,
            "input_tokens": 0,
            "output_tokens": 0,
            # API返回的真实token数据
            "api_token_count": 0,
            "api_input_tokens": 0,
            "api_output_tokens": 0,
            "structured_output": False,
            "output_mode": "text",
            "api_status": "unknown",
        }

        try:
            模型实例 = await self.获取模型(使用的模型名称, **参数)

            if not 模型实例:
                错误信息 = f"模型 {使用的模型名称} 实例化失败，请检查配置"
                模型日志器.error(f"❌ {错误信息}")
                基础返回结构.update(
                    {
                        "error": 错误信息,
                        "processing_time": (datetime.now() - 开始时间).total_seconds(),
                    }
                )
                return 基础返回结构

            模型日志器.info(f"✅ 模型实例获取成功: {type(模型实例).__name__}")

            # 转换消息格式为LangChain格式
            langchain消息列表 = []
            for i, 消息 in enumerate(消息列表):
                消息角色 = 消息.get("role", "user")
                消息内容 = 消息.get("content", "")

                模型日志器.debug(f"📨 消息 {i + 1}: {消息角色} - {消息内容[:50]}...")

                if 消息角色 == "system":
                    langchain消息列表.append(SystemMessage(content=消息内容))
                elif 消息角色 == "user":
                    langchain消息列表.append(HumanMessage(content=消息内容))
                elif 消息角色 == "assistant":
                    langchain消息列表.append(AIMessage(content=消息内容))

            模型日志器.info(
                f"📚 LangChain消息列表构建完成: {len(langchain消息列表)} 条消息"
            )

            # 处理结构化输出 - 使用LangChain官方标准模式
            使用的模型 = 模型实例

            if 结构化输出模式 and pydantic_模型类:
                模型日志器.info(f"🔧 启用结构化输出: {pydantic_模型类.__name__}")

                # 为qwen-turbo模型特殊处理 - 自动添加JSON指令
                if 使用的模型名称 and "qwen" in 使用的模型名称.lower():
                    模型日志器.info("🎯 检测到qwen模型，添加JSON输出指令")

                    # 检查是否已有系统消息包含json关键词
                    有json指令 = any(
                        isinstance(msg, SystemMessage) and "json" in msg.content.lower()
                        for msg in langchain消息列表
                    )

                    if not 有json指令:
                        # 获取Pydantic模型的字段信息
                        模型字段 = pydantic_模型类.model_fields
                        字段描述列表 = []
                        示例数据 = {}

                        for 字段名, 字段信息 in 模型字段.items():
                            字段类型 = (
                                str(字段信息.annotation)
                                .replace("typing.", "")
                                .replace("<class '", "")
                                .replace("'>", "")
                            )
                            字段描述 = getattr(字段信息, "description", "无描述")
                            是否必需 = 字段信息.is_required()

                            # 安全获取默认值，避免PydanticUndefinedType序列化错误
                            默认值 = None
                            try:
                                if (
                                    hasattr(字段信息, "default")
                                    and 字段信息.default is not None
                                ):
                                    # 检查是否为PydanticUndefinedType或其他不可序列化类型
                                    if hasattr(
                                        字段信息.default, "__class__"
                                    ) and "Undefined" not in str(
                                        字段信息.default.__class__
                                    ):
                                        默认值 = 字段信息.default
                            except Exception:
                                默认值 = None

                            字段描述列表.append(
                                f"- {字段名} ({字段类型}): {字段描述} {'[必需]' if 是否必需 else '[可选]'}"
                            )

                            # 生成示例数据
                            if 字段类型 == "str" or "str" in 字段类型:
                                if (
                                    默认值
                                    and isinstance(默认值, str)
                                    and 默认值 != "..."
                                ):
                                    示例数据[字段名] = 默认值
                                else:
                                    示例数据[字段名] = f"示例{字段名}"
                            elif 字段类型 == "int":
                                示例数据[字段名] = 1
                            elif 字段类型 == "float":
                                示例数据[字段名] = 1.0
                            elif 字段类型 == "bool":
                                示例数据[字段名] = True
                            else:
                                示例数据[字段名] = f"示例{字段名}"

                        # 构建qwen-turbo专用的JSON指令 - 包含具体字段要求
                        qwen_json_指令 = f"""请严格按照以下JSON格式输出回复。这是强制要求 - 您必须使用有效的JSON格式。

必须输出的字段：
{chr(10).join(字段描述列表)}

JSON格式示例：
{json.dumps(示例数据, ensure_ascii=False, indent=2)}

输出要求：
1. 必须使用上述字段名（中文字段名）
2. 使用标准JSON语法，字符串用双引号包围
3. 确保JSON结构完整有效
4. 不要在JSON前后添加任何其他文本
5. 不要使用"response"等其他字段名

请确保您的回复是纯JSON格式，包含所有必需字段。"""

                        # 在消息列表开头添加JSON指令 - 使用LangChain消息对象
                        json_系统消息 = SystemMessage(content=qwen_json_指令)
                        langchain消息列表.insert(0, json_系统消息)
                        模型日志器.info("✅ 已为qwen模型添加详细JSON输出指令")
                        模型日志器.debug(
                            f"🔧 JSON指令包含字段: {list(示例数据.keys())}"
                        )

                try:
                    # Context7最佳实践：使用with_structured_output方法
                    使用的模型 = 模型实例.with_structured_output(pydantic_模型类)
                    模型日志器.info(
                        f"✅ 结构化输出模型创建成功 (模型: {pydantic_模型类.__name__})"
                    )
                    模型日志器.debug(
                        f"🔧 Pydantic模型字段: {list(pydantic_模型类.model_fields.keys())}"
                    )
                except AttributeError as e:
                    模型日志器.error(f"❌ 模型不支持with_structured_output方法: {e}")
                    模型日志器.info("⚠️ 降级使用普通模式")
                except Exception as e:
                    模型日志器.error(f"❌ 创建结构化输出失败: {e}")
                    模型日志器.info("⚠️ 降级使用普通模式")

            # 调用模型API - 支持结构化输出降级处理
            模型日志器.info(f"📡 开始调用模型API: {使用的模型名称}")
            模型响应 = None
            结构化输出失败_需要降级 = False

            try:
                # 在调用前记录请求信息并直接获取原始响应
                模型日志器.info("🔍 ===== 准备调用模型API =====")

                # 获取API配置用于直接调用
                模型日志器.info(f"🔍 检查模型对象属性: {type(使用的模型)}")

                # 检查各种可能的属性名
                client = None
                model_name = None
                api_key = None
                base_url = None
                实际模型对象 = None

                # 如果是RunnableSequence（结构化输出模式），需要找到其中的ChatOpenAI对象
                if hasattr(使用的模型, "steps") and 使用的模型.steps:
                    模型日志器.info("🔍 检测到RunnableSequence，查找ChatOpenAI对象...")
                    for step in 使用的模型.steps:
                        模型日志器.info(f"🔍 检查步骤: {type(step)}")
                        if "ChatOpenAI" in str(type(step)):
                            实际模型对象 = step
                            模型日志器.info(
                                f"🔍 找到ChatOpenAI对象: {type(实际模型对象)}"
                            )
                            break
                        # 检查是否有bound属性（可能是绑定的模型）
                        elif hasattr(step, "bound") and "ChatOpenAI" in str(
                            type(step.bound)
                        ):
                            实际模型对象 = step.bound
                            模型日志器.info(
                                f"🔍 找到绑定的ChatOpenAI对象: {type(实际模型对象)}"
                            )
                            break
                else:
                    # 直接是ChatOpenAI对象
                    实际模型对象 = 使用的模型

                if 实际模型对象:
                    模型日志器.info(f"🔍 使用模型对象: {type(实际模型对象)}")

                    # 尝试获取client
                    if hasattr(实际模型对象, "client"):
                        client = 实际模型对象.client
                        模型日志器.info(f"🔍 找到client属性: {type(client)}")
                    elif hasattr(实际模型对象, "_client"):
                        client = 实际模型对象._client
                        模型日志器.info(f"🔍 找到_client属性: {type(client)}")

                    # 尝试获取model_name
                    if hasattr(实际模型对象, "model_name"):
                        model_name = 实际模型对象.model_name
                        模型日志器.info(f"🔍 找到model_name: {model_name}")
                    elif hasattr(实际模型对象, "model"):
                        model_name = 实际模型对象.model
                        模型日志器.info(f"🔍 找到model: {model_name}")

                    if client and model_name:
                        # 尝试获取API配置
                        模型日志器.info(f"🔍 Client对象类型: {type(client)}")
                        模型日志器.info(
                            f"🔍 Client对象属性: {[attr for attr in dir(client) if not attr.startswith('_')]}"
                        )

                        # 尝试从client获取配置
                        if hasattr(client, "api_key"):
                            api_key = client.api_key
                        elif hasattr(client, "_client") and hasattr(
                            client._client, "api_key"
                        ):
                            api_key = client._client.api_key

                        if hasattr(client, "base_url"):
                            base_url = str(client.base_url).rstrip("/")
                        elif hasattr(client, "_client") and hasattr(
                            client._client, "base_url"
                        ):
                            base_url = str(client._client.base_url).rstrip("/")

                        # 如果还是没有找到，尝试从实际模型对象获取
                        if not api_key and hasattr(实际模型对象, "openai_api_key"):
                            api_key = 实际模型对象.openai_api_key
                        if not base_url and hasattr(实际模型对象, "openai_api_base"):
                            base_url = str(实际模型对象.openai_api_base).rstrip("/")
                        elif not base_url and hasattr(实际模型对象, "base_url"):
                            base_url = str(实际模型对象.base_url).rstrip("/")

                        模型日志器.info(f"📡 模型名称: {model_name}")
                        模型日志器.info(f"📡 API基础URL: {base_url}")
                        模型日志器.info(
                            f"📡 API密钥: {api_key[:8] if api_key else 'None'}****"
                        )

                    # 构建请求数据用于直接API调用
                    formatted_messages = []
                    for msg in langchain消息列表:
                        if hasattr(msg, "type") and hasattr(msg, "content"):
                            role = (
                                "user"
                                if msg.type == "human"
                                else "assistant"
                                if msg.type == "ai"
                                else "system"
                            )
                            formatted_messages.append(
                                {"role": role, "content": msg.content}
                            )

                    request_data = {"model": model_name, "messages": formatted_messages}

                    # 如果是结构化输出，添加相关参数
                    if 结构化输出模式 and pydantic_模型类:
                        # 添加结构化输出参数
                        if hasattr(pydantic_模型类, "model_json_schema"):
                            schema = pydantic_模型类.model_json_schema()
                            request_data["response_format"] = {
                                "type": "json_schema",
                                "json_schema": {
                                    "name": schema.get("title", "GeneratedModel"),
                                    "schema": schema,
                                    "strict": True,
                                },
                            }
                            模型日志器.info(
                                f"📋 添加结构化输出参数: {request_data['response_format']}"
                            )

                    # 添加其他可能的参数
                    if hasattr(使用的模型, "temperature"):
                        request_data["temperature"] = 使用的模型.temperature
                    if hasattr(使用的模型, "max_tokens"):
                        request_data["max_tokens"] = 使用的模型.max_tokens

                    模型日志器.info(
                        f"📋 请求数据: {json.dumps(request_data, ensure_ascii=False, indent=2)}"
                    )

                    # 直接调用API获取原始响应
                    try:
                        async with httpx.AsyncClient() as http_client:
                            response = await http_client.post(
                                f"{base_url}/chat/completions",
                                headers={
                                    "Authorization": f"Bearer {api_key}",
                                    "Content-Type": "application/json",
                                },
                                json=request_data,
                                timeout=30.0,
                            )

                            模型日志器.info("🔍 ===== 模型层原始HTTP响应 =====")
                            模型日志器.info(f"📊 HTTP状态码: {response.status_code}")
                            模型日志器.info(f"📊 响应头: {dict(response.headers)}")

                            # 获取原始响应文本
                            raw_response_text = response.text
                            模型日志器.info(f"📊 原始响应文本: {raw_response_text}")

                            # 尝试解析JSON
                            try:
                                raw_response_json = response.json()
                                模型日志器.info(
                                    f"📊 原始响应JSON: {json.dumps(raw_response_json, ensure_ascii=False, indent=2)}"
                                )

                                # 保存原始响应用于后续token提取
                                原始API响应 = raw_response_json
                            except Exception as json_error:
                                模型日志器.error(f"❌ JSON解析失败: {json_error}")
                                原始API响应 = {"raw_text": raw_response_text}

                            模型日志器.info("🔍 ===== 原始HTTP响应记录完毕 =====")
                    except Exception as api_error:
                        模型日志器.error(f"❌ 直接API调用失败: {api_error}")
                        原始API响应 = None
                else:
                    模型日志器.warning("⚠️ 无法获取模型API配置，跳过原始响应获取")
                    原始API响应 = None

                # 正常调用LangChain
                模型响应 = await 使用的模型.ainvoke(langchain消息列表)
                模型日志器.info("✅ 模型API调用成功")
                模型日志器.debug(f"📄 响应类型: {type(模型响应).__name__}")

                # 输出大语言模型原始响应数据
                模型日志器.info("🔍 ===== 大语言模型原始响应数据 =====")
                模型日志器.info(f"📋 原始响应对象: {模型响应}")
                模型日志器.info(f"📋 原始响应类型: {type(模型响应)}")

                # 如果响应对象有属性，输出所有属性
                if hasattr(模型响应, "__dict__"):
                    模型日志器.info(f"📋 响应对象属性: {模型响应.__dict__}")

                # 输出响应内容
                if hasattr(模型响应, "content"):
                    模型日志器.info(f"📋 响应内容: {模型响应.content}")

                # 详细检查所有可能的属性
                模型日志器.info("🔍 ===== 详细属性检查 =====")
                for attr_name in dir(模型响应):
                    if not attr_name.startswith("_"):  # 跳过私有属性
                        try:
                            attr_value = getattr(模型响应, attr_name)
                            if not callable(attr_value):  # 跳过方法
                                模型日志器.info(f"📋 属性 {attr_name}: {attr_value}")
                        except Exception as e:
                            模型日志器.debug(f"🔍 无法获取属性 {attr_name}: {e}")

                # 如果有usage信息，输出token使用情况
                if hasattr(模型响应, "usage_metadata"):
                    模型日志器.info(
                        f"📋 Token使用情况 (usage_metadata): {模型响应.usage_metadata}"
                    )
                elif hasattr(模型响应, "response_metadata"):
                    模型日志器.info(
                        f"📋 响应元数据 (response_metadata): {模型响应.response_metadata}"
                    )

                # 检查是否有其他可能的token相关属性
                token_related_attrs = [
                    "usage",
                    "token_usage",
                    "tokens",
                    "token_count",
                    "llm_output",
                ]
                for attr in token_related_attrs:
                    if hasattr(模型响应, attr):
                        模型日志器.info(
                            f"📋 Token相关属性 {attr}: {getattr(模型响应, attr)}"
                        )

                # 如果是结构化输出，输出模型数据
                if hasattr(模型响应, "model_dump"):
                    模型日志器.info(f"📋 结构化数据: {模型响应.model_dump()}")

                模型日志器.info("🔍 ===== 原始响应数据输出完毕 =====")

                # Context7最佳实践：检查结构化输出响应
                if 结构化输出模式 and pydantic_模型类:
                    if hasattr(模型响应, "model_dump"):
                        模型日志器.info("✅ 收到结构化输出响应 (Pydantic对象)")
                        模型日志器.debug(f"🔍 结构化数据: {模型响应.model_dump()}")
                    else:
                        模型日志器.warning("⚠️ 期望结构化输出但收到非Pydantic对象")

            except Exception as e:
                错误信息 = str(e)
                模型日志器.error(f"❌ 模型API调用失败: {错误信息}")

                # 检查是否是结构化输出相关错误
                if 结构化输出模式 and (
                    "validation error" in 错误信息.lower()
                    or "model_type" in 错误信息.lower()
                    or "input should be an object" in 错误信息.lower()
                ):
                    模型日志器.warning("⚠️ 结构化输出验证失败，启动降级模式")
                    结构化输出失败_需要降级 = True

                    # 使用普通模式重新调用
                    try:
                        模型日志器.info("🔄 使用普通模式重新调用模型")
                        普通模型 = (
                            模型实例  # 使用原始模型实例，不使用with_structured_output
                        )
                        模型响应 = await 普通模型.ainvoke(langchain消息列表)
                        模型日志器.info("✅ 降级模式调用成功")
                    except Exception as e2:
                        模型日志器.error(f"❌ 降级模式也失败: {str(e2)}")
                        基础返回结构.update(
                            {
                                "error": f"模型调用完全失败: 结构化模式失败({错误信息})，普通模式失败({str(e2)})",
                                "processing_time": (
                                    datetime.now() - 开始时间
                                ).total_seconds(),
                                "api_status": "failed",
                            }
                        )
                        return 基础返回结构
                else:
                    # 非结构化输出相关错误，直接返回
                    基础返回结构.update(
                        {
                            "error": f"模型API调用失败: {错误信息}",
                            "processing_time": (
                                datetime.now() - 开始时间
                            ).total_seconds(),
                            "api_status": "failed",
                        }
                    )
                    return 基础返回结构

            处理时长 = (datetime.now() - 开始时间).total_seconds()
            模型日志器.info(f"⏱️ 模型调用处理时长: {处理时长:.2f}秒")

            # 处理响应内容 - Context7优化的智能降级处理
            if 结构化输出模式 and pydantic_模型类 and not 结构化输出失败_需要降级:
                模型日志器.info("🔧 处理结构化输出响应 (Context7优化版本)")
                try:
                    # Context7最佳实践：优先使用Pydantic v2方法
                    if hasattr(模型响应, "model_dump"):
                        # Pydantic v2 - 推荐方法
                        结构化数据 = 模型响应.model_dump()
                        响应内容 = json.dumps(结构化数据, ensure_ascii=False, indent=2)
                        模型日志器.info("✅ 使用Pydantic v2 model_dump()序列化")
                        结构化输出成功 = True
                        输出模式 = "structured_json"

                        # 验证数据完整性
                        if self._验证结构化输出数据(结构化数据, pydantic_模型类):
                            模型日志器.info("✅ 结构化输出数据验证通过")
                        else:
                            模型日志器.warning("⚠️ 结构化输出数据验证失败")

                    elif hasattr(模型响应, "json"):
                        # Pydantic v1兼容
                        响应内容 = 模型响应.json(ensure_ascii=False, indent=2)
                        模型日志器.info("📊 使用Pydantic v1 json()序列化")
                        结构化输出成功 = True
                        输出模式 = "structured_json"
                    elif hasattr(模型响应, "dict"):
                        # Pydantic v1兼容
                        响应内容 = json.dumps(
                            模型响应.dict(), ensure_ascii=False, indent=2
                        )
                        模型日志器.info("📊 使用Pydantic v1 dict()序列化")
                        结构化输出成功 = True
                        输出模式 = "structured_json"
                    else:
                        # 尝试解析为字符串形式的JSON
                        if hasattr(模型响应, "content"):
                            原始内容 = 模型响应.content
                        elif isinstance(模型响应, dict):
                            原始内容 = 模型响应
                        elif isinstance(模型响应, str):
                            原始内容 = 模型响应
                        else:
                            原始内容 = str(模型响应)

                        模型日志器.warning(
                            f"⚠️ 收到非结构化响应，尝试解析: {str(原始内容)[:100]}..."
                        )

                        # 尝试将内容包装为JSON格式
                        包装后内容 = await self._尝试包装为JSON格式(
                            原始内容, pydantic_模型类
                        )
                        if 包装后内容:
                            响应内容 = 包装后内容
                            结构化输出成功 = True
                            输出模式 = "wrapped_json"
                            模型日志器.info("✅ 成功包装为JSON格式")
                        else:
                            # 如果包装失败，将原始内容转换为字符串
                            if isinstance(原始内容, dict):
                                响应内容 = str(原始内容)
                            elif isinstance(原始内容, str):
                                响应内容 = 原始内容
                            else:
                                响应内容 = str(原始内容)
                            结构化输出成功 = False
                            输出模式 = "text_fallback"
                            模型日志器.warning("⚠️ JSON包装失败，使用原始文本")

                except Exception as e:
                    模型日志器.error(f"❌ 结构化响应处理失败: {str(e)}")
                    响应内容 = getattr(模型响应, "content", str(模型响应))
                    结构化输出成功 = False
                    输出模式 = "text_fallback"
            else:
                # 普通文本模式或降级模式
                响应内容 = getattr(模型响应, "content", str(模型响应))
                结构化输出成功 = False
                if 结构化输出失败_需要降级:
                    输出模式 = "degraded_text"
                    模型日志器.info("📝 使用降级文本模式")
                else:
                    输出模式 = "text"

            # 计算令牌数量（改进的代码估算）
            def 估算令牌数(文本内容):
                """
                基于官方文档重新设计的token估算算法：

                官方示例分析：
                - 中文: "你好，我是通义千问" → ['你好', '，', '我是', '通', '义', '千', '问'] (7tokens)
                - 英文: "Nice to meet you." → ['Nice', ' to', ' meet', ' you', '.'] (5tokens)

                关键发现：
                1. 中文常用词组会被合并为1个token（如"你好"、"我是"）
                2. 英文按单词+空格分割，平均3.4字符/token
                3. 标点符号通常独立成token
                """
                if not 文本内容:
                    return 0

                import re

                # 中文token估算：考虑常用词组合并
                中文字符 = re.findall(r"[\u4e00-\u9fff]", 文本内容)
                中文字符数 = len(中文字符)

                # 中文常用双字词估算（粗略估算，实际需要词典）
                # 假设30%的中文字符会组成双字词
                估算中文双字词数 = 中文字符数 * 0.3 // 2
                估算中文单字数 = 中文字符数 - (估算中文双字词数 * 2)
                中文tokens = 估算中文双字词数 + 估算中文单字数

                # 英文token估算：按单词分割
                英文单词 = re.findall(r"[a-zA-Z]+", 文本内容)
                英文tokens = len(英文单词)

                # 数字token估算
                数字序列 = re.findall(r"\d+", 文本内容)
                数字tokens = len(数字序列)

                # 标点符号token估算（每个标点基本是1个token）
                标点符号 = re.findall(r"[^\w\s\u4e00-\u9fff]", 文本内容)
                标点tokens = len(标点符号)

                # 空格通常与相邻单词合并，不单独计算

                总估算tokens = 中文tokens + 英文tokens + 数字tokens + 标点tokens

                return max(1, int(总估算tokens))

            # 计算输入token（包含所有消息）
            估算输入令牌数 = sum(估算令牌数(msg.get("content", "")) for msg in 消息列表)
            估算输出令牌数 = 估算令牌数(str(响应内容))
            估算总令牌数 = 估算输入令牌数 + 估算输出令牌数

            # 尝试从API响应中获取真实token使用情况
            真实输入令牌数 = 0
            真实输出令牌数 = 0
            真实总令牌数 = 0

            模型日志器.info("🔍 ===== 开始提取Token使用情况 =====")

            # 检查usage_metadata中的token信息
            if hasattr(模型响应, "usage_metadata") and 模型响应.usage_metadata:
                usage_data = 模型响应.usage_metadata
                模型日志器.info(f"📊 从usage_metadata获取token信息: {usage_data}")
                模型日志器.info(f"📊 usage_metadata类型: {type(usage_data)}")

                # 如果是字典类型
                if isinstance(usage_data, dict):
                    真实输入令牌数 = (
                        usage_data.get("input_tokens")
                        or usage_data.get("prompt_tokens")
                        or usage_data.get("input_token_count")
                        or 0
                    )
                    真实输出令牌数 = (
                        usage_data.get("output_tokens")
                        or usage_data.get("completion_tokens")
                        or usage_data.get("output_token_count")
                        or 0
                    )
                    真实总令牌数 = (
                        usage_data.get("total_tokens")
                        or usage_data.get("total_token_count")
                        or (真实输入令牌数 + 真实输出令牌数)
                    )
                # 如果是对象类型，尝试获取属性
                else:
                    for attr in ["input_tokens", "prompt_tokens", "input_token_count"]:
                        if hasattr(usage_data, attr):
                            真实输入令牌数 = getattr(usage_data, attr, 0)
                            break
                    for attr in [
                        "output_tokens",
                        "completion_tokens",
                        "output_token_count",
                    ]:
                        if hasattr(usage_data, attr):
                            真实输出令牌数 = getattr(usage_data, attr, 0)
                            break
                    for attr in ["total_tokens", "total_token_count"]:
                        if hasattr(usage_data, attr):
                            真实总令牌数 = getattr(usage_data, attr, 0)
                            break
                    if 真实总令牌数 == 0:
                        真实总令牌数 = 真实输入令牌数 + 真实输出令牌数

            # 如果usage_metadata没有，检查response_metadata
            elif hasattr(模型响应, "response_metadata") and 模型响应.response_metadata:
                response_data = 模型响应.response_metadata
                模型日志器.info(f"📊 从response_metadata获取token信息: {response_data}")
                模型日志器.info(f"📊 response_metadata类型: {type(response_data)}")

                # 检查是否有usage字段
                if isinstance(response_data, dict) and "usage" in response_data:
                    usage_data = response_data["usage"]
                    模型日志器.info(f"📊 response_metadata.usage: {usage_data}")
                    真实输入令牌数 = (
                        usage_data.get("input_tokens")
                        or usage_data.get("prompt_tokens")
                        or 0
                    )
                    真实输出令牌数 = (
                        usage_data.get("output_tokens")
                        or usage_data.get("completion_tokens")
                        or 0
                    )
                    真实总令牌数 = usage_data.get("total_tokens") or (
                        真实输入令牌数 + 真实输出令牌数
                    )

            # 检查其他可能的token相关属性
            else:
                模型日志器.info("📊 在其他属性中查找token信息...")
                token_attrs = ["usage", "token_usage", "tokens", "llm_output"]
                for attr in token_attrs:
                    if hasattr(模型响应, attr):
                        attr_value = getattr(模型响应, attr)
                        模型日志器.info(f"📊 找到属性 {attr}: {attr_value}")
                        if isinstance(attr_value, dict):
                            if (
                                "input_tokens" in attr_value
                                or "prompt_tokens" in attr_value
                            ):
                                真实输入令牌数 = (
                                    attr_value.get("input_tokens")
                                    or attr_value.get("prompt_tokens")
                                    or 0
                                )
                                真实输出令牌数 = (
                                    attr_value.get("output_tokens")
                                    or attr_value.get("completion_tokens")
                                    or 0
                                )
                                真实总令牌数 = attr_value.get("total_tokens") or (
                                    真实输入令牌数 + 真实输出令牌数
                                )
                                break

            # 如果还是没有找到token信息，尝试从直接API调用的原始响应中获取
            if 真实总令牌数 == 0 and "原始API响应" in locals() and 原始API响应:
                模型日志器.info("📊 从直接API调用的原始响应中获取token信息...")
                模型日志器.info(f"📊 原始API响应数据: {原始API响应}")

                # 检查usage字段
                if isinstance(原始API响应, dict) and "usage" in 原始API响应:
                    usage_data = 原始API响应["usage"]
                    模型日志器.info(f"📊 从原始API响应usage获取: {usage_data}")
                    # 阿里云API使用的字段名
                    真实输入令牌数 = usage_data.get("prompt_tokens", 0)
                    真实输出令牌数 = usage_data.get("completion_tokens", 0)
                    真实总令牌数 = usage_data.get(
                        "total_tokens", 真实输入令牌数 + 真实输出令牌数
                    )

            模型日志器.info(
                f"📊 Token提取结果: 输入={真实输入令牌数}, 输出={真实输出令牌数}, 总计={真实总令牌数}"
            )
            模型日志器.info("🔍 ===== Token使用情况提取完毕 =====")

            模型日志器.info("📊 Token统计对比:")
            模型日志器.info(
                f"   代码估算 - 输入:{估算输入令牌数}, 输出:{估算输出令牌数}, 总计:{估算总令牌数}"
            )
            模型日志器.info(
                f"   API真实 - 输入:{真实输入令牌数}, 输出:{真实输出令牌数}, 总计:{真实总令牌数}"
            )
            模型日志器.info(f"🎉 模型调用完全成功: {使用的模型名称}")

            # 返回统一的中文字段名格式，包含两种token计算方式
            return {
                "success": True,
                "response": 响应内容,
                "model_name": 使用的模型名称,
                "model_provider": "阿里云",
                "processing_time": 处理时长,
                # 代码估算的token数据（简单字符数/4计算）
                "代码估算令牌总数": 估算总令牌数,
                "代码估算输入令牌": 估算输入令牌数,
                "代码估算输出令牌": 估算输出令牌数,
                # API返回的真实token数据（包含系统提示词、JSON Schema等）
                "API真实令牌总数": 真实总令牌数,
                "API真实输入令牌": 真实输入令牌数,
                "API真实输出令牌": 真实输出令牌数,
                "structured_output": 结构化输出成功,
                "output_mode": 输出模式,
                "api_status": "success",
            }

        except Exception as e:
            处理时长 = (datetime.now() - 开始时间).total_seconds()
            模型日志器.error(f"❌ 模型调用过程发生异常: {str(e)}")

            基础返回结构.update(
                {
                    "error": f"模型调用异常: {str(e)}",
                    "processing_time": 处理时长,
                    "api_status": "error",
                }
            )
            return 基础返回结构

    async def _尝试包装为JSON格式(
        self, 原始内容: str, pydantic_模型类
    ) -> Optional[str]:
        """尝试将普通文本响应包装为JSON格式 - 智能适配结构化输出"""
        try:
            import json

            # 获取模型的字段信息
            if hasattr(pydantic_模型类, "model_fields"):
                # Pydantic v2
                字段信息 = pydantic_模型类.model_fields
            elif hasattr(pydantic_模型类, "__fields__"):
                # Pydantic v1
                字段信息 = pydantic_模型类.__fields__
            else:
                模型日志器.warning("⚠️ 无法获取模型字段信息")
                return None

            # 1. 尝试直接解析为JSON - 处理已经是JSON格式的内容
            try:
                if isinstance(原始内容, str):
                    parsed_json = json.loads(原始内容.strip())
                elif isinstance(原始内容, dict):
                    parsed_json = 原始内容
                else:
                    parsed_json = None

                if isinstance(parsed_json, dict):
                    # 检查是否需要字段映射
                    映射后JSON = await self._映射JSON字段(parsed_json, 字段信息)
                    return json.dumps(映射后JSON, ensure_ascii=False, indent=2)
            except json.JSONDecodeError:
                pass
            except Exception as e:
                模型日志器.debug(f"JSON解析失败: {str(e)}")

            # 2. 智能包装策略：根据字段类型创建合理的JSON结构
            包装结果 = {}

            # 获取所有必需字段
            必需字段 = []
            可选字段 = []

            for 字段名, 字段定义 in 字段信息.items():
                # 判断字段是否必需
                是否必需 = True
                if hasattr(字段定义, "is_required"):
                    是否必需 = 字段定义.is_required()
                elif hasattr(字段定义, "default"):
                    是否必需 = 字段定义.default == ...
                elif hasattr(字段定义, "_attributes_set"):
                    是否必需 = "default" not in 字段定义._attributes_set

                if 是否必需:
                    必需字段.append(字段名)
                else:
                    可选字段.append(字段名)

            # 主要内容字段映射（优先级从高到低）
            主要内容字段 = [
                "主要回答",
                "回答",
                "answer",
                "response",
                "内容",
                "content",
                "结果",
                "result",
                "输出",
                "output",
            ]

            # 为主要内容字段分配值
            已分配主要内容 = False
            for 字段名 in 主要内容字段:
                if 字段名 in 字段信息:
                    包装结果[字段名] = 原始内容.strip()
                    已分配主要内容 = True
                    break

            # 如果没有找到主要内容字段，使用第一个必需的字符串字段
            if not 已分配主要内容:
                for 字段名 in 必需字段:
                    字段定义 = 字段信息[字段名]
                    if self._是字符串类型(字段定义):
                        包装结果[字段名] = 原始内容.strip()
                        已分配主要内容 = True
                        break

            # 补充其他必需字段的默认值
            for 字段名 in 必需字段:
                if 字段名 not in 包装结果:
                    字段定义 = 字段信息[字段名]
                    默认值 = self._获取字段默认值(字段定义)
                    包装结果[字段名] = 默认值

            if 包装结果:
                模型日志器.info(f"🎯 智能包装JSON成功，字段数: {len(包装结果)}")
                return json.dumps(包装结果, ensure_ascii=False, indent=2)

            return None

        except Exception as e:
            模型日志器.error(f"❌ JSON包装过程失败: {str(e)}")
            return None

    async def _映射JSON字段(self, 原始JSON: dict, 字段信息: dict) -> dict:
        """映射JSON字段到期望的字段名"""
        try:
            映射后结果 = {}

            # 常见字段映射关系
            字段映射表 = {
                "response": ["主要回答", "回答", "answer", "content", "内容"],
                "answer": ["主要回答", "回答", "response", "content", "内容"],
                "content": ["主要回答", "回答", "answer", "response", "内容"],
                "result": ["结果", "主要回答", "回答", "answer", "response"],
                "output": ["输出", "主要回答", "回答", "answer", "response"],
            }

            # 已使用的目标字段
            已使用字段 = set()

            # 第一轮：直接匹配
            for 源字段, 源值 in 原始JSON.items():
                if 源字段 in 字段信息:
                    映射后结果[源字段] = 源值
                    已使用字段.add(源字段)

            # 第二轮：智能映射
            for 源字段, 源值 in 原始JSON.items():
                if 源字段 in 已使用字段:
                    continue

                # 查找映射目标
                目标字段列表 = 字段映射表.get(源字段.lower(), [])
                for 目标字段 in 目标字段列表:
                    if 目标字段 in 字段信息 and 目标字段 not in 已使用字段:
                        映射后结果[目标字段] = 源值
                        已使用字段.add(目标字段)
                        模型日志器.debug(f"📝 字段映射: {源字段} → {目标字段}")
                        break

            # 第三轮：补充缺失的必需字段
            for 字段名, 字段定义 in 字段信息.items():
                if 字段名 not in 映射后结果:
                    默认值 = self._获取字段默认值(字段定义)
                    映射后结果[字段名] = 默认值

            return 映射后结果

        except Exception as e:
            模型日志器.error(f"❌ 字段映射失败: {str(e)}")
            return 原始JSON

    def _是字符串类型(self, 字段定义) -> bool:
        """判断字段是否为字符串类型"""
        try:
            if hasattr(字段定义, "annotation"):
                类型注解 = 字段定义.annotation
            elif hasattr(字段定义, "type_"):
                类型注解 = 字段定义.type_
            else:
                return False

            return (
                类型注解 is str
                or str(类型注解) == "<class 'str'>"
                or str(类型注解).startswith("typing.Optional[str")
                or str(类型注解).startswith("Optional[str")
            )
        except Exception as e:
            模型日志器.warning(f"检查字段类型失败: {str(e)}")
            return False

    def _获取字段默认值(self, 字段定义):
        """根据字段类型获取合理的默认值"""
        try:
            if hasattr(字段定义, "annotation"):
                类型注解 = 字段定义.annotation
            elif hasattr(字段定义, "type_"):
                类型注解 = 字段定义.type_
            else:
                return ""

            # 处理 Optional 类型
            类型字符串 = str(类型注解)

            if "str" in 类型字符串 or 类型注解 is str:
                return ""
            elif "int" in 类型字符串 or 类型注解 is int:
                return 0
            elif "float" in 类型字符串 or 类型注解 is float:
                return 0.0
            elif "bool" in 类型字符串 or 类型注解 is bool:
                return False
            elif "list" in 类型字符串.lower() or "List" in 类型字符串:
                return []
            elif "dict" in 类型字符串.lower() or "Dict" in 类型字符串:
                return {}
            else:
                return ""

        except Exception as e:
            模型日志器.debug(f"获取默认值失败: {str(e)}")
            return ""

    async def 获取可用模型列表(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 从数据库获取完整的模型信息
            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            数据库模型列表 = await LangChain模型数据层实例.获取所有模型统计概览()

            模型列表 = []

            for 数据库模型 in 数据库模型列表:
                模型名称 = 数据库模型["模型名称"]
                配置 = self.模型配置.get(模型名称, {})

                模型信息 = {
                    "模型标识": 模型名称,
                    "模型名称": 模型名称,
                    "显示名称": 数据库模型.get("显示名称", 模型名称),
                    "名称": 数据库模型.get("显示名称", 模型名称),
                    "类型": 数据库模型.get("提供商", "未知"),  # 供应商信息
                    "提供商": 数据库模型.get("提供商", "未知"),
                    "模型类型": 数据库模型.get("模型类型", "chat"),  # 真正的模型类型
                    "模型名": 配置.get("模型名", 模型名称),
                    "状态": "已加载" if 模型名称 in self.模型实例 else "未加载",
                    "是否已加载": 模型名称 in self.模型实例,
                    "默认参数": 配置.get("默认参数", {}),
                    "数据库ID": 数据库模型.get("id"),
                    "算力消耗": 配置.get("算力消耗", 1),
                    "描述": 配置.get("描述", ""),
                    "头像": 配置.get("头像", ""),
                    "提示词模板": 配置.get("提示词模板", ""),
                    "最大令牌数": 配置.get("最大令牌数", 4000),
                    "最大令牌": 配置.get("最大令牌数", 4000),
                    "支持功能": 配置.get("支持功能", ["对话"]),
                    "是否默认": 模型名称 == self.默认模型,
                }
                模型列表.append(模型信息)

            return 模型列表

        except Exception as e:
            模型日志器.error(f"获取模型列表失败: {str(e)}")
            return []

    async def 添加模型配置(self, 模型名称: str, 配置: Dict[str, Any]) -> bool:
        """添加新的模型配置"""
        try:
            self.模型配置[模型名称] = 配置
            模型日志器.info(f"成功添加模型配置: {模型名称}")
            return True

        except Exception as e:
            模型日志器.error(f"添加模型配置失败 {模型名称}: {str(e)}")
            return False

    async def 删除模型(self, 模型名称: str) -> bool:
        """删除模型"""
        try:
            if 模型名称 in self.模型实例:
                del self.模型实例[模型名称]

            if 模型名称 in self.模型配置:
                del self.模型配置[模型名称]

            模型日志器.info(f"成功删除模型: {模型名称}")
            return True

        except Exception as e:
            模型日志器.error(f"删除模型失败 {模型名称}: {str(e)}")
            return False

    async def 设置默认模型(self, 模型名称: str) -> bool:
        """设置默认模型"""
        try:
            if 模型名称 not in self.模型配置:
                raise ValueError(f"模型不存在: {模型名称}")

            self.默认模型 = 模型名称
            模型日志器.info(f"设置默认模型: {模型名称}")
            return True

        except Exception as e:
            模型日志器.error(f"设置默认模型失败 {模型名称}: {str(e)}")
            return False

    def 获取状态(self) -> Dict[str, Any]:
        """获取模型管理器状态"""
        return {
            "已初始化": self.已初始化,
            "默认模型": self.默认模型,
            "已加载模型数量": len(self.模型实例),
            "可用模型数量": len(self.模型配置),
            "OpenAI可用": OPENAI_AVAILABLE,
            "LangChain可用": LANGCHAIN_AVAILABLE,
            "阿里云配置状态": "已配置" if self.阿里云配置.get("api_key") else "未配置",
        }

    async def 刷新模型配置(self) -> bool:
        """刷新模型配置（重新从数据库加载）"""
        try:
            模型日志器.info("开始刷新模型配置...")

            # 清空现有配置
            self.模型配置.clear()
            self.模型实例.clear()

            # 重新加载配置
            await self._初始化默认模型配置()

            # 预加载默认模型
            await self._预加载模型(self.默认模型)

            模型日志器.info("模型配置刷新完成")
            return True

        except Exception as e:
            模型日志器.error(f"刷新模型配置失败: {str(e)}")
            return False

    async def 获取模型详情(self, 模型名称: str) -> Optional[Dict[str, Any]]:
        """获取指定模型的详细信息"""
        try:
            if not self.已初始化:
                await self.初始化()

            配置 = self.模型配置.get(模型名称)
            if not 配置:
                return None

            return {
                "模型标识": 模型名称,
                "名称": 配置["名称"],
                "类型": 配置["类型"],
                "模型名": 配置.get("模型名", 模型名称),
                "状态": "已加载" if 模型名称 in self.模型实例 else "未加载",
                "默认参数": 配置.get("默认参数", {}),
                "数据库ID": 配置.get("数据库ID"),
                "算力消耗": 配置.get("算力消耗", 1),
                "描述": 配置.get("描述", ""),
                "头像": 配置.get("头像", ""),
                "提示词模板": 配置.get("提示词模板", ""),
                "最大令牌": 配置.get("最大令牌", 4000),
                "支持功能": 配置.get("支持功能", ["对话"]),
                "是否默认": 模型名称 == self.默认模型,
            }

        except Exception as e:
            模型日志器.error(f"获取模型详情失败 {模型名称}: {str(e)}")
            return None

    async def 测试模型连接(
        self, 模型名称: str, 测试消息: str = "你好"
    ) -> Dict[str, Any]:
        """测试模型连接"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 获取模型实例
            模型实例 = await self.获取模型(模型名称)
            if not 模型实例:
                return {"success": False, "error": f"无法获取模型实例: {模型名称}"}

            # 测试对话
            测试结果 = await self.调用模型(
                消息列表=[{"role": "user", "content": 测试消息}], 模型名称=模型名称
            )

            if 测试结果.get("success"):
                return {
                    "success": True,
                    "response": 测试结果.get("response"),
                    "processing_time": 测试结果.get("processing_time"),
                    "token_count": 测试结果.get("token_count"),
                }
            else:
                return {"success": False, "error": 测试结果.get("error", "测试失败")}

        except Exception as e:
            模型日志器.error(f"测试模型连接失败 {模型名称}: {str(e)}")
            return {"success": False, "error": f"测试连接异常: {str(e)}"}

    # ==================== 管理端专用方法 ====================

    async def 获取可用模型列表_管理端(self, 查询参数: Dict[str, Any]) -> Dict[str, Any]:
        """获取可用模型列表（管理端专用，支持分页和筛选）"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 获取所有模型
            模型列表 = await self.获取可用模型列表()

            # 应用筛选条件
            if 查询参数.get("供应商名称"):
                模型列表 = [
                    m for m in 模型列表 if 查询参数["供应商名称"] in m.get("类型", "")
                ]

            if 查询参数.get("搜索关键词"):
                关键词 = 查询参数["搜索关键词"].lower()
                模型列表 = [
                    m
                    for m in 模型列表
                    if 关键词 in m.get("名称", "").lower()
                    or 关键词 in m.get("模型标识", "").lower()
                ]

            # 处理分页
            页码 = 查询参数.get("页码", 1)
            每页数量 = 查询参数.get("每页数量", 20)
            总数量 = len(模型列表)

            开始索引 = (页码 - 1) * 每页数量
            结束索引 = 开始索引 + 每页数量
            分页模型列表 = 模型列表[开始索引:结束索引]

            模型日志器.info(
                f"获取可用模型列表（管理端）成功，总数: {总数量}，当前页: {len(分页模型列表)}"
            )

            return {
                "列表": 分页模型列表,
                "总数": 总数量,
                "当前页": 页码,
                "每页数量": 每页数量,
                "总页数": (总数量 + 每页数量 - 1) // 每页数量,
            }

        except Exception as e:
            模型日志器.error(f"获取可用模型列表（管理端）失败: {str(e)}")
            raise

    async def 测试模型连接_管理端(self, 测试数据: Dict[str, Any]) -> Dict[str, Any]:
        """测试模型连接（管理端专用）"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 确定要测试的模型
            模型名称 = None

            if 测试数据.get("模型id"):
                # 通过数据库ID查找模型
                模型列表 = await self.获取可用模型列表()
                for 模型 in 模型列表:
                    if 模型.get("数据库ID") == 测试数据["模型id"]:
                        模型名称 = 模型["模型标识"]
                        break

                if not 模型名称:
                    return {
                        "success": False,
                        "error": f"模型不存在，ID: {测试数据['模型id']}",
                    }

            elif 测试数据.get("模型名称"):
                模型名称 = 测试数据["模型名称"]
            else:
                return {"success": False, "error": "请提供模型id或模型名称"}

            # 测试模型连接
            测试结果 = await self.测试模型连接(
                模型名称=模型名称, 测试消息=测试数据.get("测试消息", "你好")
            )

            模型日志器.info(
                f"管理端测试模型连接，模型: {模型名称}，结果: {测试结果.get('success')}"
            )
            return 测试结果

        except Exception as e:
            模型日志器.error(f"管理端测试模型连接失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def 设置默认模型_管理端(self, 模型名称: str) -> Dict[str, Any]:
        """设置默认模型（管理端专用）"""
        try:
            if not self.已初始化:
                await self.初始化()

            设置结果 = await self.设置默认模型(模型名称)

            if 设置结果:
                模型日志器.info(f"管理端设置默认模型成功: {模型名称}")
                return {
                    "success": True,
                    "默认模型": 模型名称,
                    "设置时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                }
            else:
                return {"success": False, "error": "设置默认模型失败"}

        except Exception as e:
            模型日志器.error(f"管理端设置默认模型失败: {str(e)}")
            return {"success": False, "error": str(e)}

    async def 获取模型详情_管理端(self, 模型id: int) -> Optional[Dict[str, Any]]:
        """获取模型详情（管理端专用，通过数据库ID）"""
        try:
            if not self.已初始化:
                await self.初始化()

            # 通过数据库ID查找模型名称
            模型列表 = await self.获取可用模型列表()
            模型名称 = None

            for 模型 in 模型列表:
                if 模型.get("数据库ID") == 模型id:
                    模型名称 = 模型.get("模型标识") or 模型.get("模型名")
                    break

            if not 模型名称:
                模型日志器.warning(f"模型不存在，ID: {模型id}")
                return None

            模型详情 = await self.获取模型详情(模型名称)

            if 模型详情:
                模型日志器.info(
                    f"获取模型详情（管理端）成功，ID: {模型id}, 名称: {模型名称}"
                )
                return 模型详情
            else:
                模型日志器.warning(f"模型详情获取失败，名称: {模型名称}")
                return None

        except Exception as e:
            模型日志器.error(f"获取模型详情（管理端）失败: {str(e)}")
            raise

    # ==================== 模型配置管理功能 ====================

    async def 创建模型配置(self, 模型配置数据: Dict[str, Any]) -> Dict[str, Any]:
        """创建模型配置"""
        try:
            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            # 数据验证
            必需字段 = ["模型名称", "显示名称", "提供商", "模型类型"]
            for 字段 in 必需字段:
                if not 模型配置数据.get(字段):
                    return {"status": 400, "message": f"缺少必需字段: {字段}"}

            # 检查模型名称是否重复
            模型名称存在 = await LangChain模型数据层实例.检查模型名称是否存在(
                模型配置数据["模型名称"]
            )
            if 模型名称存在:
                return {"status": 400, "message": "模型名称已存在，请使用其他名称"}

            # 处理模型参数JSON序列化
            模型参数 = 模型配置数据.get("模型参数", {})
            if isinstance(模型参数, dict):
                模型配置数据["模型参数"] = json.dumps(模型参数, ensure_ascii=False)

            # 处理模型类型映射
            模型类型映射 = {
                "text": "text-generation",
                "embedding": "text-embedding",
                "chat": "chat-completion",
                "completion": "text-completion",
            }

            原始类型 = 模型配置数据.get("模型类型", "")
            模型配置数据["模型类型"] = 模型类型映射.get(原始类型.lower(), 原始类型)

            # 设置默认值
            if "最大令牌数" not in 模型配置数据:
                模型配置数据["最大令牌数"] = 4000
            if "算力消耗" not in 模型配置数据:
                模型配置数据["算力消耗"] = 1

            # 创建模型配置
            模型id = await LangChain模型数据层实例.创建模型配置(模型配置数据)

            if 模型id:
                # 刷新模型配置缓存
                await self.刷新模型配置()

                模型日志器.info(
                    f"创建模型配置成功: {模型配置数据['模型名称']}, ID: {模型id}"
                )
                return {
                    "status": 100,
                    "message": {
                        "模型id": 模型id,
                        "模型名称": 模型配置数据["模型名称"],
                        "显示名称": 模型配置数据["显示名称"],
                        "创建时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    },
                }
            else:
                return {"status": 500, "message": {"error": "创建模型配置失败"}}

        except Exception as e:
            模型日志器.error(f"创建模型配置失败: {str(e)}")
            return {"status": 500, "message": {"error": "创建模型配置失败"}}

    async def 更新模型配置(
        self, 模型id: int, 更新数据: Dict[str, Any]
    ) -> Dict[str, Any]:
        """更新模型配置"""
        try:
            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            # 检查模型是否存在
            模型详情 = await LangChain模型数据层实例.获取模型配置详情(模型id)
            if not 模型详情:
                return {"status": 404, "message": {"error": "模型不存在"}}

            # 如果要更新模型名称，检查是否重复
            if "模型名称" in 更新数据:
                新模型名称 = 更新数据["模型名称"]
                if 新模型名称 != 模型详情["模型名称"]:
                    模型名称存在 = await LangChain模型数据层实例.检查模型名称是否存在(
                        新模型名称, 模型id
                    )
                    if 模型名称存在:
                        return {
                            "status": 400,
                            "message": {"error": "模型名称已存在，请使用其他名称"},
                        }

            # 处理JSON序列化
            if "模型参数" in 更新数据 and isinstance(更新数据["模型参数"], dict):
                更新数据["模型参数"] = json.dumps(
                    更新数据["模型参数"], ensure_ascii=False
                )

            # 处理布尔值转换
            布尔字段 = ["启用状态"]
            for 字段 in 布尔字段:
                if 字段 in 更新数据:
                    if isinstance(更新数据[字段], str):
                        更新数据[字段] = int(
                            更新数据[字段].lower() in ["true", "1", "yes", "on"]
                        )
                    elif isinstance(更新数据[字段], bool):
                        更新数据[字段] = int(更新数据[字段])

            更新成功 = await LangChain模型数据层实例.更新模型配置(模型id, 更新数据)

            if 更新成功:
                # 刷新模型配置缓存
                await self.刷新模型配置()

                模型日志器.info(f"更新模型配置成功: ID {模型id}")
                return {
                    "status": 100,
                    "message": {
                        "模型id": 模型id,
                        "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "更新字段": list(更新数据.keys()),
                    },
                }
            else:
                return {
                    "status": 500,
                    "message": {
                        "error": "更新模型配置失败，可能模型不存在或没有可更新的字段"
                    },
                }

        except Exception as e:
            模型日志器.error(f"更新模型配置失败: {str(e)}")
            return {"status": 500, "message": {"error": "更新模型配置失败"}}

    async def 删除模型配置(self, 模型id: int) -> Dict[str, Any]:
        """删除模型配置"""
        try:
            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            # 检查模型是否存在
            模型详情 = await LangChain模型数据层实例.获取模型配置详情(模型id)
            if not 模型详情:
                return {"status": 404, "message": {"error": "模型不存在"}}

            删除成功 = await LangChain模型数据层实例.删除模型配置(模型id)

            if 删除成功:
                # 刷新模型配置缓存
                await self.刷新模型配置()

                模型日志器.info(
                    f"删除模型配置成功: ID {模型id}, 名称: {模型详情['模型名称']}"
                )
                return {
                    "status": 100,
                    "message": {
                        "模型id": 模型id,
                        "模型名称": 模型详情["模型名称"],
                        "删除时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    },
                }
            else:
                return {
                    "status": 500,
                    "message": {"error": "删除模型配置失败，可能模型不存在"},
                }

        except Exception as e:
            模型日志器.error(f"删除模型配置失败: {str(e)}")
            return {"status": 500, "message": {"error": "删除模型配置失败"}}

    async def 获取向量模型(self, 模型名称: Optional[str] = None) -> Optional[Any]:
        """获取向量模型实例 - 仅用于非测试场景的向量模型获取"""
        try:
            if not self.已初始化:
                await self.初始化()

            使用模型 = 模型名称 or self.默认向量模型
            if not 使用模型:
                模型日志器.warning("没有可用的向量模型")
                return None

            # 如果模型未预加载，则加载它
            if 使用模型 not in self.向量模型实例:
                向量模型实例 = await self._预加载向量模型(使用模型)
                if not 向量模型实例:
                    模型日志器.warning(f"预加载向量模型失败: {使用模型}")
                    return None

            return self.向量模型实例.get(使用模型)

        except Exception as e:
            模型日志器.error(f"获取向量模型失败 {模型名称}: {str(e)}")
            return None

    async def _预加载向量模型(self, 模型名称: str):
        """预加载向量模型实例"""
        try:
            if 模型名称 in self.向量模型实例:
                return self.向量模型实例[模型名称]

            模型配置 = self.向量模型配置.get(模型名称)
            if not 模型配置:
                模型日志器.warning(f"未找到向量模型配置: {模型名称}")
                return None

            提供商 = 模型配置.get("提供商", "").lower()
            API密钥 = 模型配置.get("API密钥", "")
            API基础URL = 模型配置.get("API基础URL", "")
            模型参数 = 模型配置.get("模型参数", {})

            向量模型实例 = None

            if "openai" in 提供商:
                # OpenAI向量模型
                向量模型实例 = OpenAIEmbeddings(
                    openai_api_key=API密钥,
                    openai_api_base=API基础URL,
                    model=模型名称,
                    **模型参数,
                )

            elif "alibaba" in 提供商 or "阿里" in 提供商:
                # 阿里云向量模型
                if not API密钥:
                    模型日志器.error(f"阿里云向量模型API密钥未配置: {模型名称}")
                    return None

                try:
                    向量模型实例 = DashScopeEmbeddings(
                        dashscope_api_key=API密钥, model=模型名称, **模型参数
                    )
                except Exception as dashscope_error:
                    模型日志器.error(
                        f"阿里云向量模型初始化失败: {str(dashscope_error)}"
                    )
                    return None

            elif "google" in 提供商:
                # Google向量模型
                if not API密钥:
                    模型日志器.error(f"Google向量模型API密钥未配置: {模型名称}")
                    return None

                try:
                    向量模型实例 = GoogleGenerativeAIEmbeddings(
                        google_api_key=API密钥, model=模型名称, **模型参数
                    )
                except Exception as google_error:
                    模型日志器.error(f"Google向量模型初始化失败: {str(google_error)}")
                    return None
            else:
                模型日志器.error(f"不支持的向量模型提供商: {提供商}")
                return None

            if 向量模型实例:
                self.向量模型实例[模型名称] = 向量模型实例
                模型日志器.info(f"向量模型预加载成功: {模型名称}")
            else:
                模型日志器.warning(f"向量模型预加载失败: {模型名称}")

            return 向量模型实例

        except Exception as e:
            模型日志器.error(f"预加载向量模型失败 {模型名称}: {str(e)}")
            return None

    async def _创建真实向量模型实例(self, 模型配置: Dict[str, Any]) -> Optional[Any]:
        """根据数据库配置创建真实的向量模型实例"""
        try:
            模型名称 = 模型配置["模型名称"]
            提供商 = 模型配置.get("提供商", "").lower()
            API密钥 = 模型配置.get("API密钥", "")
            API基础URL = 模型配置.get("API基础URL", "")

            if not API密钥:
                return {"success": False, "error": f"模型 {模型名称} 缺少API密钥"}

            # 解析模型参数
            # 模型参数 = {}  # 暂时不使用
            if 模型配置.get("模型参数"):
                try:
                    import json

                    # 模型参数 = json.loads(模型配置['模型参数'])  # 暂时不使用
                    json.loads(模型配置["模型参数"])  # 仅验证格式
                except (json.JSONDecodeError, ValueError) as e:
                    模型日志器.warning(f"解析模型参数失败: {str(e)}")
                    pass

            模型日志器.info(f"创建向量模型实例: {模型名称}, 提供商: {提供商}")

            if "openai" in 提供商:
                try:
                    from langchain_openai import OpenAIEmbeddings

                    # 构建OpenAI向量模型参数
                    openai_params = {"openai_api_key": API密钥, "model": 模型名称}
                    if API基础URL:
                        openai_params["openai_api_base"] = API基础URL

                    return OpenAIEmbeddings(**openai_params)
                except ImportError as e:
                    return {"success": False, "error": f"OpenAI库未安装: {str(e)}"}

            elif "alibaba" in 提供商 or "阿里" in 提供商:
                try:
                    from langchain_community.embeddings import DashScopeEmbeddings

                    # DashScopeEmbeddings只接受特定参数，不传递额外参数
                    return DashScopeEmbeddings(
                        dashscope_api_key=API密钥, model=模型名称
                    )
                except ImportError as e:
                    return {"success": False, "error": f"DashScope库未安装: {str(e)}"}

            elif "google" in 提供商:
                try:
                    from langchain_google_genai import GoogleGenerativeAIEmbeddings

                    # Google向量模型只接受特定参数
                    return GoogleGenerativeAIEmbeddings(
                        google_api_key=API密钥, model=模型名称
                    )
                except ImportError as e:
                    return {"success": False, "error": f"Google AI库未安装: {str(e)}"}
            else:
                return {"success": False, "error": f"不支持的向量模型提供商: {提供商}"}

        except Exception as e:
            模型日志器.error(
                f"创建向量模型实例失败 {模型配置.get('模型名称')}: {str(e)}"
            )
            return {"success": False, "error": f"创建向量模型实例失败: {str(e)}"}

    async def 测试向量模型(self, 测试数据: Dict[str, Any]) -> Dict[str, Any]:
        """测试向量模型 - 使用数据库中的真实配置"""
        try:
            模型名称 = 测试数据.get("模型名称")
            测试文本列表 = 测试数据.get("测试文本列表", [])

            if not 模型名称 or not 测试文本列表:
                return {"success": False, "error": "模型名称和测试文本列表不能为空"}

            # 从数据库获取向量模型配置
            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            模型配置 = await LangChain模型数据层实例.根据模型名称获取配置(模型名称)

            if not 模型配置:
                return {
                    "success": False,
                    "error": f"向量模型不存在: {模型名称}",
                }

            if "embedding" not in 模型配置.get("模型类型", "").lower():
                return {
                    "success": False,
                    "error": f"不是向量模型: {模型配置.get('模型类型')}",
                }

            # 创建向量模型实例
            向量模型结果 = await self._创建真实向量模型实例(模型配置)
            if isinstance(向量模型结果, dict) and not 向量模型结果.get("success", True):
                return 向量模型结果
            向量模型 = 向量模型结果

            # 执行向量化测试
            if hasattr(向量模型, "aembed_documents"):
                向量结果 = await 向量模型.aembed_documents(测试文本列表)
            else:
                向量结果 = 向量模型.embed_documents(测试文本列表)

            # 计算相似度分析
            相似度分析 = self._计算向量相似度(向量结果, 测试文本列表)

            return {
                "success": True,
                "模型名称": 模型名称,
                "测试文本数量": len(测试文本列表),
                "向量维度": len(向量结果[0]) if 向量结果 else 0,
                "相似度分析": 相似度分析,
                "测试时间": datetime.now().isoformat(),
                "向量详情": [
                    {
                        "序号": i + 1,
                        "测试文本": 测试文本列表[i],
                        "向量维度": len(向量结果[i]) if i < len(向量结果) else 0,
                        "状态": "成功",
                    }
                    for i in range(len(测试文本列表))
                ],
            }

        except Exception as e:
            模型日志器.error(f"测试向量模型失败: {str(e)}")
            return {"success": False, "error": f"测试向量模型失败: {str(e)}"}

    def _计算向量相似度(
        self, 向量结果: List[List[float]], 测试文本列表: List[str]
    ) -> Dict[str, Any]:
        """计算向量相似度分析"""
        try:
            import numpy as np

            if not 向量结果 or len(向量结果) < 2:
                return {"分析": "需要至少2个文本进行相似度分析"}

            # 计算余弦相似度
            向量矩阵 = np.array(向量结果)
            相似度矩阵 = np.dot(向量矩阵, 向量矩阵.T) / (
                np.linalg.norm(向量矩阵, axis=1, keepdims=True)
                * np.linalg.norm(向量矩阵, axis=1)
            )

            # 找出最相似的文本对
            最大相似度 = 0
            最相似文本对 = None

            for i in range(len(测试文本列表)):
                for j in range(i + 1, len(测试文本列表)):
                    相似度 = 相似度矩阵[i][j]
                    if 相似度 > 最大相似度:
                        最大相似度 = 相似度
                        最相似文本对 = (测试文本列表[i], 测试文本列表[j])

            return {
                "最高相似度": float(最大相似度),
                "最相似文本对": 最相似文本对,
                "平均相似度": float(
                    np.mean(相似度矩阵[np.triu_indices_from(相似度矩阵, k=1)])
                ),
            }

        except Exception as e:
            return {"分析": f"相似度计算失败: {str(e)}"}

    async def 获取向量模型列表带统计(self) -> Dict[str, Any]:
        """获取向量模型列表并附带统计信息"""
        try:
            from 数据.LangChain_模型数据层 import LangChain模型数据层实例

            # 获取所有模型列表，并筛选向量模型
            所有模型列表 = await LangChain模型数据层实例.获取启用的模型列表()

            模型日志器.info(f"获取到所有启用模型数量: {len(所有模型列表)}")
            for 模型 in 所有模型列表:
                模型日志器.info(
                    f"模型: {模型.get('模型名称')} - 类型: {模型.get('模型类型')}"
                )

            # 筛选出向量模型（embedding相关的模型类型）
            模型列表 = []
            for 模型 in 所有模型列表:
                模型类型 = 模型.get("模型类型", "")
                # 修复筛选逻辑：支持 alibaba_embedding, openai_embedding 等格式
                if "embedding" in 模型类型.lower() or 模型类型.lower().endswith(
                    "_embedding"
                ):
                    模型列表.append(模型)

            # 处理向量模型列表
            向量模型列表 = 模型列表

            模型日志器.info(f"获取向量模型列表成功，数量: {len(向量模型列表)}")
            return {
                "success": True,
                "data": {"模型列表": 向量模型列表, "总数量": len(向量模型列表)},
                "总数": len(向量模型列表),
            }

        except Exception as e:
            模型日志器.error(f"获取向量模型列表失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"获取向量模型列表失败: {str(e)}",
            }

    def _验证结构化输出数据(self, 结构化数据: Dict[str, Any], pydantic_模型类) -> bool:
        """
        验证结构化输出数据的完整性 - Context7最佳实践
        """
        try:
            if not isinstance(结构化数据, dict):
                模型日志器.error("❌ 结构化数据必须是字典格式")
                return False

            # 检查模型字段
            if hasattr(pydantic_模型类, "model_fields"):
                # Pydantic v2
                模型字段 = pydantic_模型类.model_fields
                必需字段 = [
                    name
                    for name, field in 模型字段.items()
                    if hasattr(field, "is_required") and field.is_required()
                ]
            elif hasattr(pydantic_模型类, "__fields__"):
                # Pydantic v1
                模型字段 = pydantic_模型类.__fields__
                必需字段 = [name for name, field in 模型字段.items() if field.required]
            else:
                模型日志器.warning("⚠️ 无法获取模型字段信息")
                return True  # 无法验证时假设正确

            # 检查必需字段
            缺失字段 = [字段 for 字段 in 必需字段 if 字段 not in 结构化数据]
            if 缺失字段:
                模型日志器.warning(f"⚠️ 缺失必需字段: {缺失字段}")
                return False

            # 检查额外字段
            额外字段 = [字段 for 字段 in 结构化数据.keys() if 字段 not in 模型字段]
            if 额外字段:
                模型日志器.info(f"ℹ️ 发现额外字段: {额外字段}")

            模型日志器.info(f"✅ 结构化数据验证通过: {len(结构化数据)}个字段")
            return True

        except Exception as e:
            模型日志器.error(f"❌ 结构化数据验证异常: {str(e)}")
            return False


# 创建全局模型管理器实例
LangChain模型管理器实例 = LangChain模型管理器()
