"""
LangChain 内部函数包装器 - 简化版本
基于FastAPI依赖注入的简化内部函数调用系统

主要功能：
1. 直接使用FastAPI的@tool装饰器
2. 简化的用户id注入机制
3. 统一的错误处理和日志记录
4. 直接的数据库关联

作者：系统
创建时间：2024年
简化时间：2024年
"""

import asyncio
import json
import logging
import threading
from datetime import datetime
from functools import wraps
from typing import Any, Callable, Dict, Optional

# 业务模块导入 - 常用模块
import 状态
from 数据.LangChain_工具数据层 import LangChain工具数据层实例
from 数据.用户 import 获取用户信息
from 服务.异步微信服务 import (
    异步更新微信好友下次沟通时间服务,
    异步查询微信好友消息详情服务,
)

# LangChain组件导入
try:
    from langchain_core.tools import StructuredTool, tool

    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
from 服务.达人服务 import 认领达人 as 执行认领达人

# 设置日志
内部函数包装器日志器 = logging.getLogger("LangChain.内部函数包装器")

# 线程本地存储，用于传递当前用户ID上下文
_thread_local = threading.local()

# LangChain工具组件
LANGCHAIN_AVAILABLE = False
try:
    from langchain_core.tools import tool  # type: ignore

    LANGCHAIN_AVAILABLE = True
except ImportError:
    # 创建占位符
    def tool(func):  # type: ignore
        return func


# 上下文管理函数
def 设置当前用户id(用户id: int):
    """设置当前线程的用户ID上下文"""
    _thread_local.current_user_id = 用户id


def 获取当前用户id() -> int | None:
    """获取当前线程的用户ID上下文"""
    return getattr(_thread_local, "current_user_id", None)


def 设置当前智能体id(智能体id: int):
    """设置当前线程的智能体ID上下文"""
    _thread_local.current_agent_id = 智能体id


def 获取当前智能体id() -> int | None:
    """获取当前线程的智能体ID上下文"""
    return getattr(_thread_local, "current_agent_id", None)


def 清除当前用户id():
    """清除当前线程的用户ID上下文"""
    if hasattr(_thread_local, "current_user_id"):
        delattr(_thread_local, "current_user_id")


def 清除当前智能体id():
    """清除当前线程的智能体ID上下文"""
    if hasattr(_thread_local, "current_agent_id"):
        delattr(_thread_local, "current_agent_id")


def 清除所有上下文():
    """清除当前线程的所有上下文"""
    清除当前用户id()
    清除当前智能体id()


# 工具调用日志记录函数
async def _记录工具调用日志(
    工具名称: str,
    用户ID: int,
    智能体id: int,
    调用参数: str,
    执行结果: str,
    执行状态: str,
    执行时间: float,
):
    """记录工具调用日志到数据库"""
    try:
        # 确保数据层已初始化
        if not LangChain工具数据层实例.已初始化:
            await LangChain工具数据层实例.初始化()

        # 记录工具调用日志
        await LangChain工具数据层实例.记录工具调用日志(
            用户ID=用户ID,
            智能体id=智能体id,
            工具名称=工具名称,
            调用参数=调用参数,
            执行结果=执行结果,
            执行状态=执行状态,
            执行时间=执行时间,
        )

        内部函数包装器日志器.debug(
            f"✅ 工具调用日志记录成功: {工具名称} (用户: {用户ID})"
        )

    except Exception as e:
        内部函数包装器日志器.error(f"❌ 记录工具调用日志失败 ({工具名称}): {str(e)}")


# 简化的工具装饰器
def 简化内部函数工具(名称: str, 描述: str):
    """简化的内部函数工具装饰器，直接使用LangChain的@tool"""

    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                # 如果函数是异步的，直接调用
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                return result

            except Exception as e:
                内部函数包装器日志器.error(f"❌ 内部函数工具调用失败 {名称}: {str(e)}")
                return f"调用失败: {str(e)}"

        # 设置函数属性 - 保留原始函数的详细文档字符串
        wrapper.__doc__ = func.__doc__ or 描述  # 优先使用原始函数的文档字符串
        wrapper.__name__ = 名称

        # 使用LangChain的@tool装饰器（如果可用）
        # 暂时禁用parse_docstring以避免中文解析问题
        if LANGCHAIN_AVAILABLE:
            return tool(parse_docstring=False)(wrapper)
        else:
            return wrapper

    return decorator


# 移除复杂的用户id注入包装器类，使用更简单的方式


def 创建用户工具(原始工具, 用户id: int):
    """创建用户专用工具，注入用户ID并返回LangChain StructuredTool对象"""
    if not LANGCHAIN_AVAILABLE:
        return 原始工具

    # 如果原始工具是StructuredTool，创建一个新的实例
    if hasattr(原始工具, "invoke") and hasattr(原始工具, "args_schema"):
        # 创建包装函数，自动注入用户ID并调用原始工具
        async def 用户工具包装函数(**kwargs):
            kwargs["用户id"] = 用户id
            try:
                return await 原始工具.ainvoke(kwargs)
            except Exception as e:
                内部函数包装器日志器.error(f"❌ 用户工具调用失败: {str(e)}")
                return f"调用失败: {str(e)}"

        # 创建新的StructuredTool，复制原始工具的属性
        return StructuredTool.from_function(
            func=用户工具包装函数,
            name=原始工具.name,
            description=原始工具.description,
            args_schema=原始工具.args_schema,
            return_direct=getattr(原始工具, "return_direct", False),
            coroutine=用户工具包装函数,
        )
    else:
        # 向后兼容的简化版本
        async def 用户工具包装器(*args, **kwargs):
            kwargs["用户id"] = 用户id
            try:
                if asyncio.iscoroutinefunction(原始工具):
                    return await 原始工具(*args, **kwargs)
                else:
                    return 原始工具(*args, **kwargs)
            except Exception as e:
                内部函数包装器日志器.error(f"❌ 用户工具调用失败: {str(e)}")
                return f"调用失败: {str(e)}"

        用户工具包装器.name = getattr(原始工具, "name", "未知工具")
        用户工具包装器.description = getattr(原始工具, "description", "内部函数工具")
        return 用户工具包装器


class 简化内部函数包装器:
    """简化的内部函数包装器 - 基于FastAPI依赖注入"""

    def __init__(self):
        self.已注册工具 = {}  # 工具名称 -> 工具实例
        self.工具元数据 = {}  # 工具名称 -> 元数据
        self.已初始化 = False

    async def _验证参数并记录日志(
        self, 工具名称: str, 参数字典: dict, 错误信息: str, 开始时间: datetime
    ) -> str:
        """通用参数验证失败处理"""
        当前用户id = 获取当前用户id()
        当前智能体id = 获取当前智能体id()

        内部函数包装器日志器.error(f"❌ {错误信息}")
        执行时间 = (datetime.now() - 开始时间).total_seconds()

        await _记录工具调用日志(
            工具名称,
            当前用户id,
            当前智能体id,
            str(参数字典).replace("'", '"'),
            错误信息,
            "失败",
            执行时间,
        )
        return f"参数验证失败：{错误信息}"

    async def 初始化(self):
        """初始化内部函数包装器"""
        try:
            if self.已初始化:
                return

            内部函数包装器日志器.info("🚀 开始初始化简化内部函数包装器...")

            # 注册预定义的业务函数工具
            await self._注册预定义工具()

            # 确保工具在数据库中注册
            await self._确保工具数据库注册()

            self.已初始化 = True
            内部函数包装器日志器.info(
                f"✅ 简化内部函数包装器初始化完成，注册了 {len(self.已注册工具)} 个工具"
            )

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 内部函数包装器初始化失败: {str(e)}")
            raise

    async def _注册预定义工具(self):
        """注册预定义的业务函数工具 - 简化版本"""
        try:
            if not LANGCHAIN_AVAILABLE:
                内部函数包装器日志器.warning("⚠️ LangChain不可用，跳过工具注册")
                return

            # 注册基础工具
            self._注册字符串相加工具()
            self._注册用户信息查询工具()
            self._注册认领达人工具()
            self._注册时间查询工具()
            self._注册微信好友消息详情查询工具()
            self._注册微信好友下次沟通时间更新工具()

            内部函数包装器日志器.info("✅ 预定义工具注册完成")

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 注册预定义工具失败: {str(e)}")

    def _注册字符串相加工具(self):
        """注册字符串相加工具"""

        @简化内部函数工具("字符串相加", "将两个字符串相加")
        async def 字符串相加(字符串1: str, 字符串2: str, 用户id: int = 1) -> str:
            """将两个字符串连接成一个字符串。

            这是一个基础的字符串操作工具，用于演示工具系统的基本功能。
            可以用于文本拼接、消息组合、标签生成等场景。

            Args:
                字符串1: 第一个字符串，将作为结果的前半部分。
                    支持任何文本内容，包括中文、英文、数字和特殊字符。
                    示例: "Hello"
                字符串2: 第二个字符串，将作为结果的后半部分。
                    支持任何文本内容，将直接拼接在字符串1之后。
                    示例: " World"
                用户id: 操作用户的唯一标识符，默认为1。
                    用于权限验证和操作日志记录。
                    示例: 1001

            Returns:
                str: 拼接后的完整字符串，格式为"字符串1字符串2"。

            Raises:
                参数验证失败: 当用户id无效时
                系统异常: 当字符串操作过程中发生错误时

            Examples:
                # 基本字符串拼接
                result = await 字符串相加("Hello", " World")
                # 返回: "Hello World"

                # 中文字符串拼接
                result = await 字符串相加("你好", "世界")
                # 返回: "你好世界"

                # 数字和文本混合
                result = await 字符串相加("订单号:", "20240731001")
                # 返回: "订单号:20240731001"

            Note:
                - 字符串拼接是直接连接，不会自动添加空格或分隔符
                - 支持空字符串作为输入参数
                - 所有操作都会记录到工具调用日志中
                - 这是一个演示工具，实际业务中可用于简单的文本处理需求
            """
            开始时间 = None
            当前用户id = 获取当前用户id() or 用户id
            当前智能体id = 获取当前智能体id()

            try:
                开始时间 = datetime.now()

                if not isinstance(当前用户id, int) or 当前用户id <= 0:
                    await _记录工具调用日志(
                        "字符串相加",
                        当前用户id,
                        当前智能体id,
                        f'{{"字符串1": "{字符串1}", "字符串2": "{字符串2}"}}',
                        "无效的用户id",
                        "失败",
                        0.0,
                    )
                    return "错误：无效的用户id"

                结果 = 字符串1 + 字符串2
                执行时间 = (datetime.now() - 开始时间).total_seconds()

                await _记录工具调用日志(
                    "字符串相加",
                    当前用户id,
                    当前智能体id,
                    f'{{"字符串1": "{字符串1}", "字符串2": "{字符串2}"}}',
                    结果,
                    "成功",
                    执行时间,
                )
                return 结果

            except Exception as e:
                内部函数包装器日志器.error(f"❌ 字符串相加失败: {str(e)}")
                if 开始时间:
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "字符串相加",
                        当前用户id,
                        当前智能体id,
                        f'{{"字符串1": "{字符串1}", "字符串2": "{字符串2}"}}',
                        str(e),
                        "失败",
                        执行时间,
                    )
                return f"字符串相加失败: {str(e)}"

        self.已注册工具["字符串相加"] = 字符串相加
        self.工具元数据["字符串相加"] = {
            "分类": "基础工具",
            "权限要求": "基础.使用",
            "描述": "将两个字符串相加",
        }

    def _注册用户信息查询工具(self):
        """注册统一的用户信息查询工具"""

        @简化内部函数工具("查询用户信息", "查询用户的完整信息")
        async def 查询用户信息(用户id: int) -> str:
            """查询指定用户的完整信息。

            此工具用于获取用户的详细资料，包括基本信息、权限等级、
            业务配置等关键数据。主要用于用户管理、权限验证、
            业务分析等场景。

            Args:
                用户id: 目标用户的唯一标识符，必须是正整数。
                    用于精确定位要查询的用户记录。
                    示例: 1001

            Returns:
                str: JSON格式的用户完整信息，包含以下字段：
                    - 用户id: 用户唯一标识符
                    - 昵称: 用户显示名称
                    - 手机号: 用户联系电话
                    - 邮箱: 用户邮箱地址
                    - 状态: 用户账号状态
                    - 等级: 用户权限等级
                    - 算力值: 用户可用算力点数
                    - 每日邀约次数: 用户每日可用邀约配额
                    - 是否管理员: 管理员权限标识
                    - 创建时间: 账号创建时间戳

            Raises:
                用户不存在: 当指定的用户id不存在时
                查询失败: 当数据库查询过程中发生错误时

            Examples:
                # 查询指定用户信息
                result = await 查询用户信息(1001)
                # 返回JSON格式的用户详细信息

                # 查询不存在的用户
                result = await 查询用户信息(99999)
                # 返回: "用户id 99999 不存在"

            Note:
                - 此工具可以查询任何用户的信息，需要适当的权限控制
                - 返回的信息包含敏感数据，使用时需要注意数据安全
                - 手机号和邮箱等个人信息的访问应遵循隐私保护原则
                - 所有查询操作都会记录到系统日志中
            """
            try:
                # 获取用户信息
                用户数据 = await 获取用户信息(用户id)
                if not 用户数据:
                    return f"用户id {用户id} 不存在"

                # 返回完整的用户信息
                用户信息 = {
                    "用户id": 用户数据.get("id"),
                    "昵称": 用户数据.get("昵称", ""),
                    "手机号": 用户数据.get("手机号", ""),
                    "邮箱": 用户数据.get("邮箱", ""),
                    "状态": 用户数据.get("状态", ""),
                    "等级": 用户数据.get("level", 1),
                    "算力值": 用户数据.get("算力值", 0),
                    "每日邀约次数": 用户数据.get("每日邀约次数", 0),
                    "是否管理员": 用户数据.get("is_admin", False),
                    "创建时间": str(用户数据.get("created_at", "")),
                }

                return json.dumps(用户信息, ensure_ascii=False)

            except Exception as e:
                return f"查询用户信息失败: {str(e)}"

        self.已注册工具["查询用户信息"] = 查询用户信息
        self.工具元数据["查询用户信息"] = {
            "分类": "用户管理",
            "权限要求": "用户.查询",
            "描述": "查询用户的完整信息，包括基本信息、权限信息等",
        }

        # 添加"查询我的信息"工具 - 安全版本，只使用传入的用户ID
        @简化内部函数工具("查询我的信息", "查询当前用户的完整信息（安全隔离）")
        async def 查询我的信息() -> str:
            """查询当前用户的完整信息（安全隔离版本）。

            此工具专门用于用户查询自己的信息，通过线程本地存储获取当前用户上下文，
            确保用户只能访问自己的数据，提供完整的数据安全隔离。
            主要用于个人信息查看、账号状态确认、权限等级查询等场景。

            Args:
                无参数: 此工具不需要传入参数，自动从当前会话上下文中获取用户身份。

            Returns:
                str: JSON格式的当前用户完整信息，包含以下字段：
                    - 用户id: 当前用户的唯一标识符
                    - 昵称: 用户显示名称
                    - 手机号: 用户联系电话
                    - 邮箱: 用户邮箱地址
                    - 状态: 用户账号状态
                    - 等级: 用户权限等级
                    - 算力值: 用户当前可用算力点数
                    - 每日邀约次数: 用户每日可用邀约配额
                    - 是否管理员: 管理员权限标识
                    - 创建时间: 账号创建时间戳

            Raises:
                安全检查失败: 当无法获取有效的用户ID上下文时
                用户不存在: 当用户记录在数据库中不存在时
                查询失败: 当数据库查询过程中发生错误时

            Examples:
                # 查询当前用户信息
                result = await 查询我的信息()
                # 返回当前用户的JSON格式详细信息

            Note:
                - 此工具具有完整的安全隔离机制，用户只能查看自己的信息
                - 通过线程本地存储自动获取用户身份，无需手动传入用户ID
                - 所有操作都会记录详细的安全日志，包括用户ID和智能体ID
                - 如果用户上下文丢失或无效，工具会拒绝执行并记录安全事件
                - 这是推荐的用户信息查询方式，比直接指定用户ID更安全
                - 返回的信息与"查询用户信息"工具格式完全一致
            """
            开始时间 = None
            当前用户id = None
            当前智能体id = None
            try:
                开始时间 = datetime.now()

                # 从线程本地存储获取当前用户ID和智能体ID
                当前用户id = 获取当前用户id()
                当前智能体id = 获取当前智能体id()

                if not 当前用户id or not isinstance(当前用户id, int) or 当前用户id <= 0:
                    内部函数包装器日志器.error(
                        f"❌ 安全检查失败：无效的用户ID: {当前用户id}"
                    )
                    await _记录工具调用日志(
                        "查询我的信息",
                        当前用户id,
                        当前智能体id,
                        "{}",
                        "安全检查失败",
                        "失败",
                        0.0,
                    )
                    return "安全检查失败：无法获取有效的用户ID"

                内部函数包装器日志器.info(
                    f"🔒 安全查询用户信息：用户ID={当前用户id}, 智能体ID={当前智能体id}"
                )

                # 获取用户信息
                用户数据 = await 获取用户信息(当前用户id)
                if not 用户数据:
                    内部函数包装器日志器.warning(f"⚠️ 用户不存在：ID={当前用户id}")
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "查询我的信息",
                        当前用户id,
                        当前智能体id,
                        "{}",
                        f"用户不存在: {当前用户id}",
                        "失败",
                        执行时间,
                    )
                    return f"用户(ID: {当前用户id})不存在"

                # 返回完整的用户信息
                用户信息 = {
                    "用户id": 用户数据.get("id"),
                    "昵称": 用户数据.get("昵称", ""),
                    "手机号": 用户数据.get("手机号", ""),
                    "邮箱": 用户数据.get("邮箱", ""),
                    "状态": 用户数据.get("状态", ""),
                    "等级": 用户数据.get("level", 1),
                    "算力值": 用户数据.get("算力值", 0),
                    "每日邀约次数": 用户数据.get("每日邀约次数", 0),
                    "是否管理员": 用户数据.get("is_admin", False),
                    "创建时间": str(用户数据.get("created_at", "")),
                }

                结果 = json.dumps(用户信息, ensure_ascii=False)
                执行时间 = (datetime.now() - 开始时间).total_seconds()

                # 记录成功的工具调用日志
                await _记录工具调用日志(
                    "查询我的信息",
                    当前用户id,
                    当前智能体id,
                    "{}",
                    结果,
                    "成功",
                    执行时间,
                )

                内部函数包装器日志器.info(f"✅ 安全查询成功：用户ID={当前用户id}")
                return 结果

            except Exception as e:
                内部函数包装器日志器.error(f"❌ 查询我的信息失败: {str(e)}")
                if 开始时间:
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "查询我的信息",
                        当前用户id,
                        当前智能体id,
                        "{}",
                        str(e),
                        "失败",
                        执行时间,
                    )
                return f"查询我的信息失败: {str(e)}"

        self.已注册工具["查询我的信息"] = 查询我的信息
        self.工具元数据["查询我的信息"] = {
            "分类": "用户管理",
            "权限要求": "用户.查询",
            "描述": "查询当前用户的完整信息，自动使用智能体关联的用户ID",
        }

    def _注册认领达人工具(self):
        """注册认领达人工具"""

        @简化内部函数工具("认领达人", "为用户认领指定的达人")
        async def 认领达人(达人id: int, 用户id: int) -> str:
            """为指定用户认领达人资源。

            此工具用于处理达人认领业务，将公海中的达人资源分配给特定用户，
            建立用户与达人之间的归属关系。主要用于达人资源管理、
            业务分配、团队协作等场景。

            Args:
                达人id: 目标达人的唯一标识符，必须是正整数。
                    指定要认领的达人资源，达人必须处于可认领状态。
                    示例: 5001
                用户id: 执行认领操作的用户ID，必须是正整数。
                    指定哪个用户将获得该达人的归属权。
                    示例: 1001

            Returns:
                str: 认领操作的结果描述，包含以下情况：
                    - 成功时：显示用户ID和达人ID的认领成功信息
                    - 失败时：显示具体的失败原因和错误信息

            Raises:
                业务逻辑错误: 当达人已被认领、用户权限不足、达人不存在等业务规则冲突时
                系统错误: 当数据库操作或系统处理过程中发生异常时

            Examples:
                # 基本认领操作
                result = await 认领达人(5001, 1001)
                # 成功返回: "用户id 1001 成功认领达人id 5001"

                # 认领已被占用的达人
                result = await 认领达人(5002, 1001)
                # 失败返回: "认领达人失败: 该达人已被其他用户认领"

                # 认领不存在的达人
                result = await 认领达人(99999, 1001)
                # 失败返回: "认领达人失败: 达人不存在"

            Note:
                - 认领操作具有排他性，一个达人只能被一个用户认领
                - 用户需要有足够的权限才能执行认领操作
                - 认领成功后，达人将从公海中移除，归属于指定用户
                - 所有认领操作都会记录到系统日志中，便于追踪和审计
                - 认领失败不会影响现有的达人归属关系
                - 建议在认领前先查询达人状态，确保达人可被认领
            """
            try:
                # 执行认领操作
                结果 = await 执行认领达人(用户id, 达人id)

                if 结果:
                    return f"用户id {用户id} 成功认领达人id {达人id}"
                else:
                    return f"用户id {用户id} 认领达人id {达人id} 失败"

            except ValueError as e:
                # 业务逻辑错误
                return f"认领达人失败: {str(e)}"
            except Exception as e:
                return f"认领达人系统错误: {str(e)}"

        self.已注册工具["认领达人"] = 认领达人
        self.工具元数据["认领达人"] = {
            "分类": "达人管理",
            "权限要求": "达人.认领",
            "描述": "为用户认领指定的达人",
        }

    def _注册时间查询工具(self):
        """注册时间查询工具"""

        @简化内部函数工具("获取当前时间", "获取当前系统时间")
        async def 获取当前时间() -> str:
            """获取当前系统时间。

            此工具用于获取服务器的当前时间戳，提供标准格式的时间信息。
            主要用于时间记录、日志标记、业务时间计算、定时任务等场景。

            Args:
                无参数: 此工具不需要任何输入参数，直接返回当前系统时间。

            Returns:
                str: 格式化的当前时间字符串，格式为 "YYYY-MM-DD HH:MM:SS"。
                    例如: "2024-07-31 14:30:25"

            Raises:
                系统异常: 当获取系统时间过程中发生错误时

            Examples:
                # 获取当前时间
                result = await 获取当前时间()
                # 返回: "2024-07-31 14:30:25"

                # 用于时间记录
                current_time = await 获取当前时间()
                log_message = f"操作执行时间: {current_time}"

            Note:
                - 返回的时间基于服务器所在时区的本地时间
                - 时间格式固定为 "YYYY-MM-DD HH:MM:SS"，便于标准化处理
                - 此工具执行速度快，适合频繁调用
                - 所有调用都会记录到工具调用日志中
                - 可用于生成时间戳、计算时间差、验证时间范围等业务场景
                - 时间精度为秒级，不包含毫秒信息
            """
            开始时间 = None
            当前用户id = None
            当前智能体id = None
            try:
                开始时间 = datetime.now()
                当前用户id = 获取当前用户id()
                当前智能体id = 获取当前智能体id()

                结果 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                执行时间 = (datetime.now() - 开始时间).total_seconds()

                # 记录成功的工具调用日志
                await _记录工具调用日志(
                    "获取当前时间",
                    当前用户id,
                    当前智能体id,
                    "{}",
                    结果,
                    "成功",
                    执行时间,
                )

                return 结果
            except Exception as e:
                内部函数包装器日志器.error(f"❌ 获取当前时间失败: {str(e)}")
                if 开始时间:
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "获取当前时间",
                        当前用户id,
                        当前智能体id,
                        "{}",
                        str(e),
                        "失败",
                        执行时间,
                    )
                return f"获取时间失败: {str(e)}"

        # 修复：添加工具到已注册工具字典
        self.已注册工具["获取当前时间"] = 获取当前时间
        self.工具元数据["获取当前时间"] = {
            "分类": "内部工具",
            "权限要求": "",
            "描述": "获取当前系统时间",
        }

    def _注册微信好友消息详情查询工具(self):
        """注册微信好友消息详情查询工具"""

        @简化内部函数工具(
            "查询微信好友对话消息详情",
            "查询微信好友的消息时间信息",
        )
        async def 查询微信好友对话消息详情(
            用户id: int,
            我方微信号id: int,
            识别id: int,
        ) -> str:
            """查询指定微信好友的详细消息时间信息。

            获取微信好友的完整消息记录，包括双方最后消息发送时间、下次沟通时间、
            好友关系建立时间等关键信息。用于分析沟通频率、跟进客户状态。

            Args:
                用户id: 当前用户ID，用于权限验证
                我方微信号id: 我方微信账号ID，指定查询哪个微信号下的好友
                识别id: 微信好友识别ID，唯一标识好友记录

            Returns:
                str: JSON格式的好友详细信息，包含消息时间、好友状态等数据
            """
            开始时间 = None
            当前用户id = None
            当前智能体id = None
            try:
                import json

                开始时间 = datetime.now()

                # 从线程本地存储获取当前用户ID和智能体ID（用于日志记录）
                当前用户id = 获取当前用户id()
                当前智能体id = 获取当前智能体id()

                # 参数验证
                参数字典 = {
                    "用户id": 用户id,
                    "我方微信号id": 我方微信号id,
                    "识别id": 识别id,
                }

                if not isinstance(用户id, int) or 用户id <= 0:
                    return await self._验证参数并记录日志(
                        "查询微信好友对话消息详情", 参数字典, "无效的用户ID", 开始时间
                    )

                if not isinstance(我方微信号id, int) or 我方微信号id <= 0:
                    return await self._验证参数并记录日志(
                        "查询微信好友对话消息详情",
                        参数字典,
                        "无效的我方微信号ID",
                        开始时间,
                    )

                if not isinstance(识别id, int) or 识别id <= 0:
                    return await self._验证参数并记录日志(
                        "查询微信好友对话消息详情",
                        参数字典,
                        "无效的识别ID",
                        开始时间,
                    )

                内部函数包装器日志器.info(
                    f"🔍 开始查询微信好友消息详情：用户ID={用户id}, 我方微信号ID={我方微信号id}, 识别ID={识别id}, 智能体ID={当前智能体id}"
                )

                # 调用异步微信服务
                查询结果 = await 异步查询微信好友消息详情服务(
                    用户id, 我方微信号id, 识别id
                )

                if 查询结果.get("status") != 状态.通用.成功:
                    错误信息 = 查询结果.get("message", "查询失败")
                    内部函数包装器日志器.warning(
                        f"⚠️ 查询微信好友消息详情失败: {错误信息}"
                    )

                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "查询微信好友对话消息详情",
                        当前用户id,
                        当前智能体id,
                        f'{{"用户id": {用户id}, "我方微信号id": {我方微信号id}, "识别id": {识别id}}}',
                        错误信息,
                        "失败",
                        执行时间,
                    )
                    return f"查询微信好友消息详情失败: {错误信息}"

                # 获取查询结果数据 - 修复数据结构访问
                数据部分 = 查询结果.get("data", {})
                好友详情 = (
                    数据部分.get("好友详情", {}) if isinstance(数据部分, dict) else {}
                )

                # 辅助函数：安全转换datetime为字符串
                def 安全转换时间(时间值):
                    if 时间值 is None:
                        return None
                    if hasattr(时间值, "strftime"):
                        return 时间值.strftime("%Y-%m-%d %H:%M:%S")
                    return str(时间值)

                # 格式化返回结果，突出重要的时间信息
                结果信息 = {
                    "好友基本信息": {
                        "对方微信号": 好友详情.get("对方微信号"),
                        "对方昵称": 好友详情.get("对方昵称"),
                        "我方微信号": 好友详情.get("我方微信号"),
                        "识别id": 好友详情.get("识别id"),
                        "是否失效": 好友详情.get("是否失效"),
                        "备注": 好友详情.get("备注"),
                    },
                    "消息时间信息": {
                        "对方最后一条消息发送时间": 安全转换时间(
                            好友详情.get("对方最后一条消息发送时间")
                        ),
                        "我方最后一条消息发送时间": 安全转换时间(
                            好友详情.get("我方最后一条消息发送时间")
                        ),
                        "好友入库时间": 安全转换时间(好友详情.get("好友入库时间")),
                        "好友通过时间": 安全转换时间(好友详情.get("好友通过时间")),
                        "发送请求时间": 安全转换时间(好友详情.get("发送请求时间")),
                        "创建时间": 安全转换时间(好友详情.get("创建时间")),
                    },
                }

                结果JSON = json.dumps(结果信息, ensure_ascii=False, indent=2)
                执行时间 = (datetime.now() - 开始时间).total_seconds()

                # 记录成功的工具调用日志
                await _记录工具调用日志(
                    "查询微信好友对话消息详情",
                    当前用户id,
                    当前智能体id,
                    f'{{"用户id": {用户id}, "我方微信号id": {我方微信号id}, "识别id": {识别id}}}',
                    结果JSON,
                    "成功",
                    执行时间,
                )

                内部函数包装器日志器.info(
                    f"✅ 查询微信好友消息详情成功：用户ID={用户id}, 对方微信号={好友详情.get('对方微信号')}"
                )
                return 结果JSON

            except Exception as e:
                内部函数包装器日志器.error(f"❌ 查询微信好友消息详情异常: {str(e)}")
                if 开始时间:
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "查询微信好友对话消息详情",
                        当前用户id,
                        当前智能体id,
                        f'{{"用户id": {用户id}, "我方微信号id": {我方微信号id}, "识别id": {识别id}}}',
                        str(e),
                        "失败",
                        执行时间,
                    )
                return f"查询微信好友消息详情系统异常: {str(e)}"

        self.已注册工具["查询微信好友对话消息详情"] = 查询微信好友对话消息详情
        self.工具元数据["查询微信好友对话消息详情"] = {
            "分类": "微信管理",
            "权限要求": "微信.查询",
            "描述": "查询微信好友的详细消息时间信息，包括双方最后消息时间、下次沟通时间等关键数据，用于客户跟进分析",
        }

    def _注册微信好友下次沟通时间更新工具(self):
        """注册微信好友下次沟通时间更新工具"""

        @简化内部函数工具(
            "更新微信好友下次沟通时间",
            "设置微信好友的下次沟通时间",
        )
        async def 更新微信好友下次沟通时间(
            用户id: int,
            我方微信号id: int,
            识别id: int,
            下次沟通时间: Optional[str] = None,
        ) -> str:
            """设置微信好友的下次沟通时间。

            更新微信好友表中的下次沟通时间字段，用于客户跟进管理和沟通计划安排。
            支持手动指定时间或自动计算，帮助优化客户关系维护。

            Args:
                用户id: 当前用户ID，用于权限验证
                我方微信号id: 我方微信账号ID，指定操作哪个微信号下的好友
                识别id: 微信好友识别ID，唯一标识好友记录
                下次沟通时间: 指定的沟通时间，格式为 YYYY-MM-DD HH:MM:SS，可选

            Returns:
                str: 更新操作的结果描述，包含成功状态和时间信息
            """
            开始时间 = None
            当前用户id = None
            当前智能体id = None
            try:
                开始时间 = datetime.now()

                # 从线程本地存储获取当前用户ID和智能体ID（用于日志记录）
                当前用户id = 获取当前用户id()
                当前智能体id = 获取当前智能体id()

                # 参数验证
                参数字典 = {
                    "用户id": 用户id,
                    "我方微信号id": 我方微信号id,
                    "识别id": 识别id,
                    "下次沟通时间": 下次沟通时间,
                }

                if not isinstance(用户id, int) or 用户id <= 0:
                    return await self._验证参数并记录日志(
                        "更新微信好友下次沟通时间", 参数字典, "无效的用户ID", 开始时间
                    )

                if not isinstance(我方微信号id, int) or 我方微信号id <= 0:
                    return await self._验证参数并记录日志(
                        "更新微信好友下次沟通时间",
                        参数字典,
                        "无效的我方微信号ID",
                        开始时间,
                    )

                if not isinstance(识别id, int) or 识别id <= 0:
                    return await self._验证参数并记录日志(
                        "更新微信好友下次沟通时间", 参数字典, "无效的识别ID", 开始时间
                    )

                # 验证下次沟通时间参数
                下次沟通时间对象 = None
                if 下次沟通时间 is not None:
                    try:
                        下次沟通时间对象 = datetime.strptime(
                            下次沟通时间, "%Y-%m-%d %H:%M:%S"
                        )
                    except ValueError:
                        return await self._验证参数并记录日志(
                            "更新微信好友下次沟通时间",
                            参数字典,
                            "下次沟通时间格式不正确，必须为 YYYY-MM-DD HH:MM:SS",
                            开始时间,
                        )

                内部函数包装器日志器.info(
                    f"🔄 开始更新微信好友下次沟通时间：用户ID={用户id}, 我方微信号ID={我方微信号id}, 识别id={识别id}, 下次沟通时间={下次沟通时间}, 智能体ID={当前智能体id}"
                )

                # 调用异步微信服务
                更新结果 = await 异步更新微信好友下次沟通时间服务(
                    用户id=用户id,
                    我方微信号id=我方微信号id,
                    识别id=识别id,
                    下次沟通时间=下次沟通时间对象,
                )

                if 更新结果.get("status") != 状态.通用.成功:
                    错误信息 = 更新结果.get("message", "更新失败")
                    内部函数包装器日志器.warning(
                        f"⚠️ 更新微信好友下次沟通时间失败: {错误信息}"
                    )
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "更新微信好友下次沟通时间",
                        当前用户id,
                        当前智能体id,
                        f'{{"用户id": {用户id}, "我方微信号id": {我方微信号id}, "识别id": {识别id}, "下次沟通时间": "{下次沟通时间}"}}',
                        错误信息,
                        "失败",
                        执行时间,
                    )
                    return f"更新微信好友下次沟通时间失败: {错误信息}"

                # 成功处理
                结果信息 = 更新结果.get("message", "更新成功")

                执行时间 = (datetime.now() - 开始时间).total_seconds()
                await _记录工具调用日志(
                    "更新微信好友下次沟通时间",
                    当前用户id,
                    当前智能体id,
                    f'{{"用户id": {用户id}, "我方微信号id": {我方微信号id}, "识别id": {识别id}, "下次沟通时间": "{下次沟通时间}"}}',
                    结果信息,
                    "成功",
                    执行时间,
                )

                内部函数包装器日志器.info(
                    f"✅ 更新微信好友下次沟通时间成功：用户ID={用户id}, 我方微信号ID={我方微信号id}, 识别ID={识别id}"
                )

                # 构建返回消息
                返回消息 = f"✅ 成功设置微信好友下次沟通时间：{结果信息}"
                if 下次沟通时间对象 is not None:
                    返回消息 += f"\n⏰ 已设置下次沟通时间：{下次沟通时间对象.strftime('%Y-%m-%d %H:%M:%S')}"

                return 返回消息

            except Exception as e:
                内部函数包装器日志器.error(f"❌ 更新微信好友下次沟通时间异常: {str(e)}")
                if 开始时间:
                    执行时间 = (datetime.now() - 开始时间).total_seconds()
                    await _记录工具调用日志(
                        "更新微信好友下次沟通时间",
                        当前用户id,
                        当前智能体id,
                        f'{{"用户id": {用户id}, "我方微信号id": {我方微信号id}, "识别id": {识别id}, "下次沟通时间": "{下次沟通时间}"}}',
                        str(e),
                        "失败",
                        执行时间,
                    )
                return f"更新微信好友下次沟通时间系统异常: {str(e)}"

        self.已注册工具["更新微信好友下次沟通时间"] = 更新微信好友下次沟通时间
        self.工具元数据["更新微信好友下次沟通时间"] = {
            "分类": "微信管理",
            "权限要求": "微信.更新",
            "描述": "设置微信好友的下次沟通时间，用于客户跟进管理和沟通计划安排，支持手动指定或自动计算",
        }

    async def _确保工具数据库注册(self):
        """确保工具在数据库中注册 - 使用统一工具数据层"""
        try:
            # 初始化工具数据层
            if not LangChain工具数据层实例.已初始化:
                await LangChain工具数据层实例.初始化()

            for 工具名称, 元数据 in self.工具元数据.items():
                工具配置 = {
                    "工具名称": 工具名称,
                    "工具描述": 元数据["描述"],
                    "工具参数": "",  # 内部函数工具参数通过装饰器定义
                    "权限要求": 元数据["权限要求"]
                    if isinstance(元数据["权限要求"], str)
                    else "",
                    "安全级别": 1,
                    "启用状态": True,
                }
                await LangChain工具数据层实例.创建工具配置(工具配置)

            内部函数包装器日志器.info("✅ 工具数据库注册完成")

        except Exception as e:
            内部函数包装器日志器.error(f"❌ 工具数据库注册失败: {str(e)}")

    def 获取用户工具列表(self, 用户id: int) -> Dict[str, Any]:
        """为指定用户获取可用的工具列表"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空工具列表")
            return {}

        用户工具 = {}
        for 工具名称, 原始工具 in self.已注册工具.items():
            # 使用简化的用户工具创建函数
            用户工具[工具名称] = 创建用户工具(原始工具, 用户id)

        内部函数包装器日志器.info(f"✅ 为用户 {用户id} 创建了 {len(用户工具)} 个工具")
        return 用户工具

    async def 获取可用工具列表(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空工具列表")
            return {}
        return self.已注册工具.copy()

    async def 获取工具元数据(self) -> Dict[str, Any]:
        """获取工具元数据"""
        if not self.已初始化:
            内部函数包装器日志器.warning("⚠️ 包装器未初始化，返回空元数据")
            return {}
        return self.工具元数据.copy()


# 创建全局实例
内部函数包装器实例 = 简化内部函数包装器()
