"""
LangChain数据库字段映射常量
统一管理数据库字段名称，确保前后端数据交互的一致性
"""


class LangChain字段映射:
    """LangChain数据库字段映射常量类"""

    # ==================== 知识库表字段映射 ====================
    知识库表字段 = {
        "ID": "id",
        "知识库名称": "知识库名称",
        "知识库描述": "知识库描述",
        "知识库状态": "知识库状态",
        "文档数量": "文档数量",
        "嵌入模型": "嵌入模型",
        "向量存储类型": "向量存储类型",
        "创建时间": "创建时间",
        "更新时间": "更新时间",
    }

    # ==================== 知识库文档表字段映射 ====================
    知识库文档表字段 = {
        "ID": "id",
        "文档UUID": "文档uuid",
        "知识id": "langchain_知识库表id",
        "文档路径": "用户文件路径",
        "文档名称": "文档名称",
        "文档类型": "文档类型",
        "文档大小": "文档大小",
        "文档内容": "文档内容",
        "文档状态": "文档状态",
        "向量状态": "向量状态",  # 统一使用向量状态
        "向量分块数量": "向量分块数量",
        "元数据": "元数据",
        "错误信息": "错误信息",
        "最后向量化时间": "最后向量化时间",
        "MD5": "md5",  # 文件MD5值
        "创建时间": "创建时间",
        "更新时间": "更新时间",
    }

    # ==================== 文档向量表字段映射 ====================
    文档向量表字段 = {
        "ID": "id",
        "文档ID": "langchain_知识库文档表id",
        "分块序号": "分块序号",
        "分块内容": "分块内容",
        "向量维度": "向量维度",
        "向量数据": "向量数据",
        "元数据": "元数据",
        "创建时间": "创建时间",
        # 注意：langchain_文档向量表没有更新时间字段，只有创建时间
    }

    # ==================== 状态枚举 ====================
    向量状态枚举 = ["待处理", "处理中", "已完成", "失败"]
    文档状态枚举 = ["待处理", "处理中", "已处理", "失败"]
    知识库状态枚举 = ["正常", "维护中", "已删除"]

    # 向量状态兼容性映射（用于统一旧的状态值）
    向量状态兼容映射 = {
        # 标准状态
        "待处理": "待处理",
        "处理中": "处理中",
        "已完成": "已完成",
        "失败": "失败",
        # 兼容旧的状态值
        "已向量化": "已完成",
        "向量化失败": "失败",
        "向量化中": "处理中",
        "待向量化": "待处理",
    }

    @classmethod
    def 标准化向量状态(cls, 状态: str) -> str:
        """标准化向量状态值"""
        return cls.向量状态兼容映射.get(状态, 状态)


# 创建全局实例
字段映射 = LangChain字段映射()
